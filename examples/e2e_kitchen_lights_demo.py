"""
E2E Demo: "Turn on the kitchen lights"
=====================================

This demonstrates the complete workflow from user prompt to Home Assistant service call.
"""
import asyncio
import json
from typing import Dict, Any, Optional

# Mock classes to simulate the actual Home Assistant environment
class MockHomeAssistant:
    """Mock Home Assistant instance for demonstration."""
    
    def __init__(self):
        self.services = MockServices()
        self.context = MockContext()
        self.states = MockStates()

class MockServices:
    """Mock Home Assistant services."""
    
    def __init__(self):
        self.call_log = []
    
    async def async_call(self, domain: str, service: str, data: Dict[str, Any], 
                        blocking: bool = True, context=None):
        """Mock service call that logs the parameters."""
        call_info = {
            "domain": domain,
            "service": service,
            "data": data,
            "blocking": blocking,
            "context_origin": getattr(context, 'origin', None) if context else None
        }
        self.call_log.append(call_info)
        print(f"🔧 Service Call: {domain}.{service}")
        print(f"   📄 Data: {json.dumps(data, indent=6)}")
        print(f"   🏷️  Origin: {call_info['context_origin']}")
        print(f"   ⏱️  Blocking: {blocking}")

class MockContext:
    """Mock Home Assistant context."""
    
    class Context:
        def __init__(self, origin: str):
            self.origin = origin

class MockStates:
    """Mock Home Assistant states."""
    
    def get(self, entity_id: str):
        # Return a mock state for any entity
        return type('State', (), {
            'entity_id': entity_id,
            'state': 'off',
            'attributes': {'friendly_name': f'{entity_id.split(".")[1].replace("_", " ").title()}'}
        })()

# Simplified version of our entity_control module for demo
async def call_service_demo(hass, domain: str, service: str, data: Optional[Dict[str, Any]] = None):
    """Demo version of call_service with thin client origin and timeout."""
    
    # Input validation
    if not domain or not domain.strip():
        raise ValueError("Service domain is required")
    if not service or not service.strip():
        raise ValueError("Service name is required")
    
    # Prepare service data
    service_data = data.copy() if data else {}
    
    # Add thin client origin for tracking
    context = hass.context.Context(origin="thin_client")
    
    print(f"📡 Calling service {domain}.{service} with origin=thin_client")
    
    # Call the service with timeout (simplified for demo)
    try:
        await asyncio.wait_for(
            hass.services.async_call(
                domain,
                service,
                service_data,
                blocking=True,
                context=context
            ),
            timeout=10.0
        )
    except asyncio.TimeoutError:
        raise Exception(f"Service call {domain}.{service} timed out after 10 seconds")
    
    return {
        "status": "success",
        "service": f"{domain}.{service}",
        "data": service_data,
        "origin": "thin_client"
    }

async def demonstrate_kitchen_lights_e2e():
    """
    Demonstrate the complete E2E workflow for "Turn on the kitchen lights".
    """
    print("🏠 Sirohi Thin Client E2E Demo: 'Turn on the kitchen lights'")
    print("=" * 70)
    
    # Step 1: User provides natural language input
    user_prompt = "Turn on the kitchen lights"
    print(f"👤 User Input: '{user_prompt}'")
    print()
    
    # Step 2: Backend AI/NLP processing (simulated)
    print("🧠 Backend AI Processing...")
    print("   • Parsing natural language")
    print("   • Identifying intent: light control")
    print("   • Extracting entities: kitchen lights")
    print("   • Mapping to Home Assistant service")
    print()
    
    # Step 3: Backend generates structured action
    backend_response = {
        "action": "call_service",
        "data": {
            "domain": "light",
            "service": "turn_on",
            "entity_id": "light.kitchen",
            "data": {
                "brightness": 255,
                "color_temp": 370,  # Warm white
                "transition": 2     # 2 second fade-in
            }
        }
    }
    
    print("📤 Backend Response:")
    print(json.dumps(backend_response, indent=2))
    print()
    
    # Step 4: Thin client processes the action
    print("⚙️  Thin Client Processing...")
    
    # Create mock Home Assistant instance
    mock_hass = MockHomeAssistant()
    
    # Extract action data
    action_data = backend_response["data"]
    
    # Prepare service data (combining entity_id with service data)
    service_data = action_data["data"].copy()
    service_data["entity_id"] = action_data["entity_id"]
    
    print(f"   • Action: {backend_response['action']}")
    print(f"   • Domain: {action_data['domain']}")
    print(f"   • Service: {action_data['service']}")
    print(f"   • Prepared service data: {json.dumps(service_data, indent=6)}")
    print()
    
    # Step 5: Execute the service call
    print("🚀 Executing Service Call...")
    
    try:
        result = await call_service_demo(
            mock_hass,
            action_data["domain"],
            action_data["service"],
            service_data
        )
        
        print("✅ Service call completed successfully!")
        print()
        
        # Step 6: Show the results
        print("📊 Results:")
        print(f"   • Status: {result['status']}")
        print(f"   • Service: {result['service']}")
        print(f"   • Origin: {result['origin']}")
        print(f"   • Data sent: {json.dumps(result['data'], indent=6)}")
        print()
        
        # Step 7: Show what happened in Home Assistant
        print("🏠 Home Assistant Service Log:")
        for i, call in enumerate(mock_hass.services.call_log, 1):
            print(f"   Call #{i}:")
            print(f"     Domain: {call['domain']}")
            print(f"     Service: {call['service']}")
            print(f"     Entity: {call['data'].get('entity_id', 'N/A')}")
            print(f"     Brightness: {call['data'].get('brightness', 'N/A')}")
            print(f"     Color Temp: {call['data'].get('color_temp', 'N/A')}")
            print(f"     Transition: {call['data'].get('transition', 'N/A')}s")
            print(f"     Origin: {call['context_origin']}")
            print(f"     Blocking: {call['blocking']}")
        print()
        
        print("🎉 Demo completed successfully!")
        print("💡 The kitchen lights would now be turning on with:")
        print("   • Full brightness (255)")
        print("   • Warm white color (370K)")
        print("   • 2-second smooth transition")
        print("   • Tracked as 'thin_client' origin")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

async def demonstrate_validation_errors():
    """Demonstrate validation error scenarios."""
    print("\n" + "=" * 70)
    print("🚨 Validation Error Demo")
    print("=" * 70)
    
    mock_hass = MockHomeAssistant()
    
    # Test invalid domain
    print("Test 1: Empty domain")
    try:
        await call_service_demo(mock_hass, "", "turn_on", {"entity_id": "light.test"})
    except ValueError as e:
        print(f"   ✅ Caught expected error: {e}")
    
    # Test invalid service
    print("\nTest 2: Empty service")
    try:
        await call_service_demo(mock_hass, "light", "", {"entity_id": "light.test"})
    except ValueError as e:
        print(f"   ✅ Caught expected error: {e}")
    
    print("\n✅ Validation tests completed!")

async def demonstrate_timeout_behavior():
    """Demonstrate timeout behavior."""
    print("\n" + "=" * 70)
    print("⏱️  Timeout Behavior Demo")
    print("=" * 70)
    
    # Create a mock that simulates a slow service
    class SlowMockServices(MockServices):
        async def async_call(self, *args, **kwargs):
            print("   ⏳ Simulating slow service (would timeout after 10s)...")
            await asyncio.sleep(0.1)  # Quick demo - real timeout would be 10s
            await super().async_call(*args, **kwargs)
    
    mock_hass = MockHomeAssistant()
    mock_hass.services = SlowMockServices()
    
    print("🔧 Testing service call with timeout protection...")
    
    result = await call_service_demo(
        mock_hass, 
        "light", 
        "turn_on", 
        {"entity_id": "light.test", "brightness": 100}
    )
    
    print(f"   ✅ Service completed within timeout: {result['status']}")

if __name__ == "__main__":
    print("🚀 Starting Sirohi Thin Client E2E Demonstrations")
    print()
    
    async def run_all_demos():
        # Main E2E demo
        success = await demonstrate_kitchen_lights_e2e()
        
        if success:
            # Additional demos
            await demonstrate_validation_errors()
            await demonstrate_timeout_behavior()
            
            print("\n" + "=" * 70)
            print("🎯 Summary")
            print("=" * 70)
            print("✅ E2E workflow: User prompt → AI processing → Service call")
            print("✅ Thin client origin tracking")
            print("✅ 10-second timeout protection")
            print("✅ Input validation")
            print("✅ Structured error handling")
            print("✅ Comprehensive logging")
            print()
            print("🔗 Integration Points:")
            print("   • JSON Schema v0.2 for action validation")
            print("   • Pydantic models for type safety")
            print("   • FastAPI backend routes")
            print("   • Home Assistant service integration")
            print("   • pytest test coverage")
    
    # Run the demo
    asyncio.run(run_all_demos())
