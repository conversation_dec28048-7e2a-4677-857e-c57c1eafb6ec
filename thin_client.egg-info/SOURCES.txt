setup.py
backend/__init__.py
backend/gemini_client.py
backend/main.py
backend/models.py
backend/test_gemini_client.py
tests/__init__.py
tests/test_api.py
tests/test_automation.py
tests/test_context.py
tests/test_entity_control.py
tests/test_scene.py
thin_client/__init__.py
thin_client.egg-info/PKG-INFO
thin_client.egg-info/SOURCES.txt
thin_client.egg-info/dependency_links.txt
thin_client.egg-info/requires.txt
thin_client.egg-info/top_level.txt