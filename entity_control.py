"""Entity control operations for Sirohi Thin Client."""
import asyncio
import logging
from typing import Dict, Any, Optional
from homeassistant.core import HomeAssistant
from homeassistant.exceptions import HomeAssistantError

_LOGGER = logging.getLogger(__name__)

async def call_service(
    hass: HomeAssistant,
    domain: str,
    service: str,
    data: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Call a Home Assistant service with thin client origin and timeout.
    
    Args:
        hass: Home Assistant instance
        domain: Service domain (e.g., 'light', 'switch')
        service: Service name (e.g., 'turn_on', 'turn_off')
        data: Service data including entity_id and other parameters
        
    Returns:
        Service call result
        
    Raises:
        ValueError: If domain or service is invalid
        HomeAssistantError: If service call fails
    """
    try:
        # Input validation
        if not domain or not domain.strip():
            raise ValueError("Service domain is required")
        
        if not service or not service.strip():
            raise ValueError("Service name is required")
        
        domain = domain.strip()
        service = service.strip()
        
        # Prepare service data with defaults
        service_data = data.copy() if data else {}
        
        # Add thin client origin for tracking
        context = getattr(hass, 'context', None)
        if context and hasattr(context, 'Context'):
            call_context = context.Context(origin="thin_client")
        else:
            call_context = None
        
        _LOGGER.debug("Calling service %s.%s with data: %s", domain, service, service_data)
        
        # Call the service with timeout
        try:
            await asyncio.wait_for(
                hass.services.async_call(
                    domain,
                    service,
                    service_data,
                    blocking=True,
                    context=call_context
                ),
                timeout=10.0  # 10 second timeout
            )
        except asyncio.TimeoutError:
            raise HomeAssistantError(f"Service call {domain}.{service} timed out after 10 seconds")
        
        _LOGGER.info("Successfully called service %s.%s with origin=thin_client", domain, service)
        
        return {
            "status": "success",
            "service": f"{domain}.{service}",
            "data": service_data,
            "origin": "thin_client"
        }
        
    except HomeAssistantError as e:
        error_msg = f"Home Assistant error calling service {domain}.{service}: {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e
        
    except ValueError as e:
        error_msg = f"Invalid service call parameters: {str(e)}"
        _LOGGER.error(error_msg)
        raise ValueError(error_msg) from e
        
    except Exception as e:
        error_msg = f"Unexpected error calling service {domain}.{service}: {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e

async def get_entity_state(hass: HomeAssistant, entity_id: str) -> Dict[str, Any]:
    """
    Get the current state of an entity.
    
    Args:
        hass: Home Assistant instance
        entity_id: The entity ID to query
        
    Returns:
        Entity state information
        
    Raises:
        ValueError: If entity_id is invalid
        HomeAssistantError: If entity not found
    """
    try:
        if not entity_id or not entity_id.strip():
            raise ValueError("Entity ID is required")
        
        entity_id = entity_id.strip()
        state = hass.states.get(entity_id)
        
        if not state:
            raise HomeAssistantError(f"Entity not found: {entity_id}")
        
        return {
            "entity_id": state.entity_id,
            "state": state.state,
            "attributes": dict(state.attributes),
            "last_changed": state.last_changed.isoformat() if state.last_changed else None,
            "last_updated": state.last_updated.isoformat() if state.last_updated else None
        }
        
    except ValueError as e:
        error_msg = f"Invalid entity ID: {str(e)}"
        _LOGGER.error(error_msg)
        raise ValueError(error_msg) from e
        
    except Exception as e:
        error_msg = f"Error getting entity state for {entity_id}: {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e

async def set_entity_state(
    hass: HomeAssistant,
    entity_id: str,
    state: str,
    attributes: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Set the state of an entity (for testing/simulation purposes).
    
    Args:
        hass: Home Assistant instance
        entity_id: The entity ID to update
        state: New state value
        attributes: Optional attributes to set
        
    Returns:
        Updated entity information
        
    Raises:
        ValueError: If parameters are invalid
        HomeAssistantError: If state setting fails
    """
    try:
        if not entity_id or not entity_id.strip():
            raise ValueError("Entity ID is required")
        
        if not state:
            raise ValueError("State value is required")
        
        entity_id = entity_id.strip()
        state = state.strip()
        
        # Set the state
        hass.states.async_set(
            entity_id,
            state,
            attributes or {}
        )
        
        _LOGGER.info("Successfully set state for entity %s to %s", entity_id, state)
        
        return {
            "status": "success",
            "entity_id": entity_id,
            "state": state,
            "attributes": attributes or {}
        }
        
    except ValueError as e:
        error_msg = f"Invalid state setting parameters: {str(e)}"
        _LOGGER.error(error_msg)
        raise ValueError(error_msg) from e
        
    except Exception as e:
        error_msg = f"Error setting state for {entity_id}: {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e

async def toggle_entity(hass: HomeAssistant, entity_id: str) -> Dict[str, Any]:
    """
    Toggle an entity's state (on/off, open/close, etc.).
    
    Args:
        hass: Home Assistant instance
        entity_id: The entity ID to toggle
        
    Returns:
        Toggle operation result
        
    Raises:
        ValueError: If entity_id is invalid
        HomeAssistantError: If toggle fails
    """
    try:
        if not entity_id or not entity_id.strip():
            raise ValueError("Entity ID is required")
        
        entity_id = entity_id.strip()
        
        # Determine domain from entity_id
        domain = entity_id.split('.')[0]
        
        # Call toggle service
        await hass.services.async_call(
            domain,
            "toggle",
            {"entity_id": entity_id},
            blocking=True
        )
        
        _LOGGER.info("Successfully toggled entity %s", entity_id)
        
        return {
            "status": "success",
            "entity_id": entity_id,
            "action": "toggle"
        }
        
    except ValueError as e:
        error_msg = f"Invalid toggle parameters: {str(e)}"
        _LOGGER.error(error_msg)
        raise ValueError(error_msg) from e
        
    except HomeAssistantError as e:
        error_msg = f"Home Assistant error toggling {entity_id}: {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e
        
    except Exception as e:
        error_msg = f"Unexpected error toggling {entity_id}: {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e
