#!/usr/bin/env python3
"""
Debug script to check environment variable loading.
"""

import os
from dotenv import load_dotenv

print("🔍 Environment Debug")
print("=" * 40)

# Check before loading .env
print("Before loading .env:")
print(f"GEMINI_API_KEY from os.environ: {os.environ.get('GEMINI_API_KEY', 'NOT_FOUND')}")

# Load .env file with override
print("\nLoading .env file with override...")
load_dotenv(override=True)

# Check after loading .env
api_key = os.getenv("GEMINI_API_KEY")
print(f"GEMINI_API_KEY from os.getenv(): {api_key}")

if api_key:
    print(f"Key length: {len(api_key)}")
    print(f"Key starts with: {api_key[:10]}...")
    print(f"Key ends with: ...{api_key[-10:]}")
    
    # Check for common issues
    if api_key.startswith(' ') or api_key.endswith(' '):
        print("⚠️  WARNING: API key has leading/trailing spaces!")
    
    if '\n' in api_key or '\r' in api_key:
        print("⚠️  WARNING: API key contains newline characters!")
        
    if len(api_key) != 39:  # Standard Gemini API key length
        print(f"⚠️  WARNING: API key length ({len(api_key)}) is unusual (expected ~39)")
else:
    print("❌ No API key found!")

# Test direct API call
print("\n🧪 Testing direct API call...")
try:
    import google.generativeai as genai
    
    print("Configuring genai...")
    genai.configure(api_key=api_key)
    
    print("Creating model...")
    model = genai.GenerativeModel('gemini-2.0-flash')
    
    print("Making API call...")
    response = model.generate_content("Say 'Hello from Python!'")
    
    if response and response.candidates:
        text = response.candidates[0].content.parts[0].text
        print(f"✅ Success! Response: {text}")
    else:
        print("❌ No response received")
        
except Exception as e:
    print(f"❌ Error: {e}")
    print(f"Error type: {type(e)}")
