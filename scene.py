import logging
from typing import Dict, Any
from homeassistant.core import HomeAssistant
from homeassistant.exceptions import HomeAssistantError

_LOGGER = logging.getLogger(__name__)

# GOAL:
# Handle scene CRUD operations with proper error handling
# All WS calls wrapped in try/except HomeAssistantError

async def create_scene(hass: HomeAssistant, scene_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create a new scene in Home Assistant.
    Returns scene details on success.
    """
    try:
        # Validate required fields
        if not scene_data.get("name"):
            raise ValueError("Scene name is required")
        
        if not scene_data.get("entities"):
            raise ValueError("Scene entities are required")
        
        # Create scene via Home Assistant service
        await hass.services.async_call(
            "scene",
            "create",
            {
                "scene_id": scene_data["name"].lower().replace(" ", "_"),
                "entities": scene_data["entities"]
            },
            blocking=True
        )
        
        _LOGGER.info("Successfully created scene: %s", scene_data.get("name"))
        return {"status": "created", "scene_id": scene_data.get("name")}
        
    except HomeAssistantError as e:
        error_msg = f"Home Assistant error creating scene: {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e
        
    except ValueError as e:
        error_msg = f"Invalid scene data: {str(e)}"
        _LOGGER.error(error_msg)
        raise ValueError(error_msg) from e
        
    except Exception as e:
        error_msg = f"Unexpected error creating scene: {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e

async def edit_scene(hass: HomeAssistant, scene_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
    """
    Edit an existing scene in Home Assistant.
    Returns updated scene details on success.
    """
    try:
        if not scene_id or not scene_id.strip():
            raise ValueError("Scene ID is required")
            
        # Check if scene exists
        entity_id = f"scene.{scene_id.lower().replace(' ', '_')}"
        state = hass.states.get(entity_id)
        
        if not state:
            raise HomeAssistantError(f"Scene not found: {scene_id}")
        
        # Update scene via Home Assistant service
        if "entities" in updates:
            await hass.services.async_call(
                "scene",
                "create",
                {
                    "scene_id": scene_id.lower().replace(" ", "_"),
                    "entities": updates["entities"]
                },
                blocking=True
            )
        
        _LOGGER.info("Successfully updated scene: %s", scene_id)
        return {"status": "updated", "scene_id": scene_id, "updates": updates}
        
    except HomeAssistantError as e:
        error_msg = f"Home Assistant error editing scene '{scene_id}': {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e
        
    except ValueError as e:
        error_msg = f"Invalid input for editing scene: {str(e)}"
        _LOGGER.error(error_msg)
        raise ValueError(error_msg) from e
        
    except Exception as e:
        error_msg = f"Unexpected error editing scene '{scene_id}': {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e

async def delete_scene(hass: HomeAssistant, scene_id: str) -> Dict[str, Any]:
    """
    Delete a scene from Home Assistant.
    Returns deletion confirmation on success.
    """
    try:
        if not scene_id or not scene_id.strip():
            raise ValueError("Scene ID is required")
            
        entity_id = f"scene.{scene_id.lower().replace(' ', '_')}"
        state = hass.states.get(entity_id)
        
        if not state:
            raise HomeAssistantError(f"Scene not found: {scene_id}")
        
        # Delete scene via Home Assistant service
        await hass.services.async_call(
            "scene",
            "delete",
            {"entity_id": entity_id},
            blocking=True
        )
        
        _LOGGER.info("Successfully deleted scene: %s", scene_id)
        return {"status": "deleted", "scene_id": scene_id}
        
    except HomeAssistantError as e:
        error_msg = f"Home Assistant error deleting scene '{scene_id}': {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e
        
    except ValueError as e:
        error_msg = f"Invalid input for deleting scene: {str(e)}"
        _LOGGER.error(error_msg)
        raise ValueError(error_msg) from e
        
    except Exception as e:
        error_msg = f"Unexpected error deleting scene '{scene_id}': {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e

async def activate_scene(hass: HomeAssistant, scene_id: str) -> Dict[str, Any]:
    """
    Activate a scene in Home Assistant.
    Returns activation confirmation on success.
    """
    try:
        if not scene_id or not scene_id.strip():
            raise ValueError("Scene ID is required")
            
        entity_id = f"scene.{scene_id.lower().replace(' ', '_')}"
        state = hass.states.get(entity_id)
        
        if not state:
            raise HomeAssistantError(f"Scene not found: {scene_id}")
        
        # Activate scene via Home Assistant service
        await hass.services.async_call(
            "scene",
            "turn_on",
            {"entity_id": entity_id},
            blocking=True
        )
        
        _LOGGER.info("Successfully activated scene: %s", scene_id)
        return {"status": "activated", "scene_id": scene_id}
        
    except HomeAssistantError as e:
        error_msg = f"Home Assistant error activating scene '{scene_id}': {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e
        
    except ValueError as e:
        error_msg = f"Invalid input for activating scene: {str(e)}"
        _LOGGER.error(error_msg)
        raise ValueError(error_msg) from e
        
    except Exception as e:
        error_msg = f"Unexpected error activating scene '{scene_id}': {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e

async def list_scenes(hass: HomeAssistant) -> Dict[str, Any]:
    """
    List all scenes in Home Assistant.
    Returns list of scene entities.
    """
    try:
        scenes = hass.states.async_all("scene")
        
        scene_list = []
        for scene in scenes:
            scene_info = {
                "entity_id": scene.entity_id,
                "name": scene.attributes.get("friendly_name", scene.entity_id),
                "state": scene.state,
                "last_activated": scene.attributes.get("last_activated"),
            }
            scene_list.append(scene_info)
        
        _LOGGER.debug("Retrieved %d scenes", len(scene_list))
        return {"scenes": scene_list, "count": len(scene_list)}
        
    except HomeAssistantError as e:
        error_msg = f"Home Assistant error listing scenes: {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e
        
    except Exception as e:
        error_msg = f"Unexpected error listing scenes: {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e
