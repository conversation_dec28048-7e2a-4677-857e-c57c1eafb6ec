import pytest
import os
from unittest.mock import patch
from api import validate_https_url, get_jitter_delay

def test_https_url_valid():
    assert validate_https_url("https://example.com")

def test_https_url_invalid():
    with pytest.raises(ValueError, match="Only HTTPS URLs are supported"):
        validate_https_url("http://example.com")

def test_cert_fingerprint_mismatch(monkeypatch):
    monkeypatch.setenv("BACKEND_CERT_FINGERPRINT", "deadbeef" * 8)
    # Use a hostname that will not match the dummy fingerprint
    with pytest.raises(ValueError, match="Certificate fingerprint mismatch|Certificate pinning failed"):
        validate_https_url("https://www.google.com")

def test_https_url_empty():
    with pytest.raises(ValueError, match="Backend URL is not set"):
        validate_https_url("")

def test_https_url_missing_hostname():
    """Test URL validation with missing hostname for cert pinning."""
    with patch.dict(os.environ, {"BACKEND_CERT_FINGERPRINT": "abc123"}):
        with pytest.raises(ValueError, match="URL missing hostname"):
            validate_https_url("https://")

def test_get_jitter_delay():
    """Test secure jitter delay generation."""
    delay = get_jitter_delay(1.0, 2.0)
    assert 1.0 <= delay <= 3.0  # base + max_jitter

    # Test multiple calls return different values (randomness)
    delays = [get_jitter_delay(1.0, 1.0) for _ in range(10)]
    assert len(set(delays)) > 1  # Should have some variation

def test_get_jitter_delay_zero_base():
    """Test jitter with zero base delay."""
    delay = get_jitter_delay(0.0, 1.0)
    assert 0.0 <= delay <= 1.0
