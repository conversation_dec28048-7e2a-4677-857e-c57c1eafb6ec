import pytest
from api import validate_https_url

def test_https_url_valid():
    assert validate_https_url("https://example.com")

def test_https_url_invalid():
    with pytest.raises(ValueError, match="Only HTTPS URLs are supported"):
        validate_https_url("http://example.com")

def test_cert_fingerprint_mismatch(monkeypatch):
    monkeypatch.setenv("BACKEND_CERT_FINGERPRINT", "deadbeef" * 8)
    # Use a hostname that will not match the dummy fingerprint
    with pytest.raises(ValueError, match="Certificate fingerprint mismatch|Certificate pinning failed"):
        validate_https_url("https://www.google.com")
