from context import redact_headers

def test_redact_headers_removes_license_key():
    headers = {
        "X-License-Key": "secret123",
        "User-Agent": "ThinClient",
        "Other": "value"
    }
    redacted = redact_headers(headers)
    assert redacted["X-License-Key"] == "[REDACTED]"
    assert redacted["User-Agent"] == "ThinClient"
    assert redacted["Other"] == "value"
    # Ensure no license_key value appears in any header
    for v in redacted.values():
        assert "secret123" not in str(v)
