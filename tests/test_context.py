import logging
from context import redact_headers, validate_ws_origin, redact

def test_redact_headers_removes_license_key():
    headers = {
        "X-License-Key": "secret123",
        "User-Agent": "ThinClient",
        "Other": "value"
    }
    redacted = redact_headers(headers)
    assert redacted["X-License-Key"] == "[REDACTED]"
    assert redacted["User-Agent"] == "ThinClient"
    assert redacted["Other"] == "value"
    # Ensure no license_key value appears in any header
    for v in redacted.values():
        assert "secret123" not in str(v)

def test_validate_ws_origin_valid():
    """Test WebSocket origin validation with valid origin."""
    allowed_origins = ["https://homeassistant.local:8123", "https://ha.example.com"]
    assert validate_ws_origin("https://homeassistant.local:8123", allowed_origins) is True
    assert validate_ws_origin("https://ha.example.com", allowed_origins) is True

def test_validate_ws_origin_invalid():
    """Test WebSocket origin validation with invalid origin."""
    allowed_origins = ["https://homeassistant.local:8123", "https://ha.example.com"]
    assert validate_ws_origin("https://malicious.com", allowed_origins) is False
    assert validate_ws_origin("http://homeassistant.local:8123", allowed_origins) is False

def test_validate_ws_origin_empty():
    """Test WebSocket origin validation with empty inputs."""
    assert validate_ws_origin("", ["https://example.com"]) is False
    assert validate_ws_origin("https://example.com", []) is False
    assert validate_ws_origin("", []) is False

def test_redact_function_comprehensive():
    """Test comprehensive redaction of sensitive data in nested structures."""
    sensitive_data = {
        "license_key": "secret123",
        "password": "mypassword",
        "token": "abc123token",
        "api_key": "apikey456",
        "secret": "topsecret",
        "auth": "authdata",
        "credential": "mycreds",
        "private": "privateinfo",
        "normal_field": "public_data",
        "nested": {
            "license_key": "nested_secret",
            "public": "visible"
        },
        "list_data": [
            {"license_key": "list_secret", "public": "ok"},
            "plain_string"
        ]
    }

    redacted = redact(sensitive_data)

    # Check all sensitive fields are redacted
    assert redacted["license_key"] == "[REDACTED]"
    assert redacted["password"] == "[REDACTED]"
    assert redacted["token"] == "[REDACTED]"
    assert redacted["api_key"] == "[REDACTED]"
    assert redacted["secret"] == "[REDACTED]"
    assert redacted["auth"] == "[REDACTED]"
    assert redacted["credential"] == "[REDACTED]"
    assert redacted["private"] == "[REDACTED]"

    # Check normal fields are preserved
    assert redacted["normal_field"] == "public_data"

    # Check nested redaction
    assert redacted["nested"]["license_key"] == "[REDACTED]"
    assert redacted["nested"]["public"] == "visible"

    # Check list redaction
    assert redacted["list_data"][0]["license_key"] == "[REDACTED]"
    assert redacted["list_data"][0]["public"] == "ok"
    assert redacted["list_data"][1] == "plain_string"

    # Ensure no sensitive values appear anywhere in the redacted output
    redacted_str = str(redacted)
    assert "secret123" not in redacted_str
    assert "mypassword" not in redacted_str
    assert "abc123token" not in redacted_str
    assert "nested_secret" not in redacted_str
    assert "list_secret" not in redacted_str

def test_redact_case_insensitive():
    """Test that redaction works with different case variations."""
    data = {
        "LICENSE_KEY": "secret1",
        "License_Key": "secret2",
        "license_KEY": "secret3",
        "X-License-Key": "secret4",
        "API_KEY": "secret5",
        "normal_key": "public"  # Should NOT be redacted
    }

    redacted = redact(data)

    assert redacted["LICENSE_KEY"] == "[REDACTED]"
    assert redacted["License_Key"] == "[REDACTED]"
    assert redacted["license_KEY"] == "[REDACTED]"
    assert redacted["X-License-Key"] == "[REDACTED]"
    assert redacted["API_KEY"] == "[REDACTED]"
    assert redacted["normal_key"] == "public"  # Should be preserved

def test_log_redaction_integration():
    """Test that logging with redacted data never exposes sensitive information."""
    import io

    # Capture log output
    log_capture = io.StringIO()
    handler = logging.StreamHandler(log_capture)
    logger = logging.getLogger("test_redaction")
    logger.addHandler(handler)
    logger.setLevel(logging.DEBUG)

    # Simulate logging sensitive data with redaction
    sensitive_config = {
        "backend_url": "https://example.com",
        "license_key": "super_secret_key_123",
        "timeout": 30
    }

    # Log the redacted version
    logger.info("Config: %s", redact(sensitive_config))

    # Check log output
    log_output = log_capture.getvalue()

    # Sensitive data should not appear in logs
    assert "super_secret_key_123" not in log_output
    assert "[REDACTED]" in log_output
    assert "https://example.com" in log_output  # Non-sensitive data should appear
    assert "30" in log_output  # Non-sensitive data should appear

    # Cleanup
    logger.removeHandler(handler)
