from context import redact_headers, validate_ws_origin

def test_redact_headers_removes_license_key():
    headers = {
        "X-License-Key": "secret123",
        "User-Agent": "ThinClient",
        "Other": "value"
    }
    redacted = redact_headers(headers)
    assert redacted["X-License-Key"] == "[REDACTED]"
    assert redacted["User-Agent"] == "ThinClient"
    assert redacted["Other"] == "value"
    # Ensure no license_key value appears in any header
    for v in redacted.values():
        assert "secret123" not in str(v)

def test_validate_ws_origin_valid():
    """Test WebSocket origin validation with valid origin."""
    allowed_origins = ["https://homeassistant.local:8123", "https://ha.example.com"]
    assert validate_ws_origin("https://homeassistant.local:8123", allowed_origins) is True
    assert validate_ws_origin("https://ha.example.com", allowed_origins) is True

def test_validate_ws_origin_invalid():
    """Test WebSocket origin validation with invalid origin."""
    allowed_origins = ["https://homeassistant.local:8123", "https://ha.example.com"]
    assert validate_ws_origin("https://malicious.com", allowed_origins) is False
    assert validate_ws_origin("http://homeassistant.local:8123", allowed_origins) is False

def test_validate_ws_origin_empty():
    """Test WebSocket origin validation with empty inputs."""
    assert validate_ws_origin("", ["https://example.com"]) is False
    assert validate_ws_origin("https://example.com", []) is False
    assert validate_ws_origin("", []) is False
