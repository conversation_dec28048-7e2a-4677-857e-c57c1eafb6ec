"""Tests for entity_control.py module."""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock
from homeassistant.core import HomeAssistant
from homeassistant.exceptions import HomeAssistantError

from entity_control import (
    call_service,
    get_entity_state,
    set_entity_state,
    toggle_entity
)

@pytest.fixture
def mock_hass():
    """Mock Home Assistant instance."""
    hass = Mock(spec=HomeAssistant)
    hass.services = Mock()
    hass.services.async_call = AsyncMock()
    hass.states = Mock()
    hass.context = Mock()
    hass.context.Context = Mock()
    return hass

class TestCallService:
    """Test the call_service function with thin client origin and timeout."""
    
    @pytest.mark.asyncio
    async def test_call_service_success(self, mock_hass):
        """Test successful service call with thin client origin."""
        # Setup mock context
        mock_context = Mock()
        mock_hass.context.Context.return_value = mock_context
        
        service_data = {
            "entity_id": "light.kitchen",
            "brightness": 255
        }
        
        result = await call_service(mock_hass, "light", "turn_on", service_data)
        
        # Assert service was called with correct parameters
        mock_hass.services.async_call.assert_called_once_with(
            "light",
            "turn_on", 
            service_data,
            blocking=True,
            context=mock_context
        )
        
        # Assert context was created with thin_client origin
        mock_hass.context.Context.assert_called_once_with(origin="thin_client")
        
        # Assert return value
        assert result["status"] == "success"
        assert result["service"] == "light.turn_on"
        assert result["data"] == service_data
        assert result["origin"] == "thin_client"
    
    @pytest.mark.asyncio
    async def test_call_service_without_context(self, mock_hass):
        """Test service call when context is not available."""
        # Remove context from hass
        del mock_hass.context
        
        service_data = {"entity_id": "light.kitchen"}
        
        result = await call_service(mock_hass, "light", "turn_on", service_data)
        
        # Should still work, just with None context
        mock_hass.services.async_call.assert_called_once_with(
            "light",
            "turn_on",
            service_data,
            blocking=True,
            context=None
        )
        
        assert result["status"] == "success"
        assert result["origin"] == "thin_client"
    
    @pytest.mark.asyncio
    async def test_call_service_timeout(self, mock_hass):
        """Test service call timeout handling."""
        # Create an async function that hangs longer than the timeout
        async def slow_service_call(*args, **kwargs):
            await asyncio.sleep(15)  # Longer than 10s timeout
            
        mock_hass.services.async_call.side_effect = slow_service_call
        
        with pytest.raises(HomeAssistantError) as exc_info:
            await call_service(mock_hass, "light", "turn_on", {"entity_id": "light.kitchen"})
        
        assert "timed out after 10 seconds" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_call_service_validation_errors(self, mock_hass):
        """Test input validation for service calls."""
        # Test empty domain
        with pytest.raises(ValueError) as exc_info:
            await call_service(mock_hass, "", "turn_on")
        assert "Service domain is required" in str(exc_info.value)
        
        # Test empty service
        with pytest.raises(ValueError) as exc_info:
            await call_service(mock_hass, "light", "")
        assert "Service name is required" in str(exc_info.value)
        
        # Test None domain
        with pytest.raises(ValueError) as exc_info:
            await call_service(mock_hass, None, "turn_on")
        assert "Service domain is required" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_call_service_homeassistant_error(self, mock_hass):
        """Test handling of HomeAssistant errors."""
        mock_hass.services.async_call.side_effect = HomeAssistantError("Service not found")
        
        with pytest.raises(HomeAssistantError) as exc_info:
            await call_service(mock_hass, "light", "turn_on")
        
        assert "Home Assistant error calling service light.turn_on" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_call_service_e2e_kitchen_lights(self, mock_hass):
        """E2E test: 'Turn on the kitchen lights' scenario."""
        # Setup mock context for thin client origin
        mock_context = Mock()
        mock_hass.context.Context.return_value = mock_context
        
        # Simulate backend response for "Turn on the kitchen lights"
        service_data = {
            "entity_id": "light.kitchen",
            "brightness": 255,
            "color_temp": 370
        }
        
        result = await call_service(mock_hass, "light", "turn_on", service_data)
        
        # Verify the service call was made correctly
        mock_hass.services.async_call.assert_called_once_with(
            "light",
            "turn_on",
            service_data,
            blocking=True,
            context=mock_context
        )
        
        # Verify thin client origin was set
        mock_hass.context.Context.assert_called_once_with(origin="thin_client")
        
        # Verify response structure
        assert result == {
            "status": "success",
            "service": "light.turn_on",
            "data": service_data,
            "origin": "thin_client"
        }

class TestGetEntityState:
    """Test entity state retrieval."""
    
    @pytest.mark.asyncio
    async def test_get_entity_state_success(self, mock_hass):
        """Test successful entity state retrieval."""
        from datetime import datetime
        
        mock_state = Mock()
        mock_state.entity_id = "light.kitchen"
        mock_state.state = "on"
        mock_state.attributes = {"brightness": 255, "friendly_name": "Kitchen Light"}
        mock_state.last_changed = datetime(2023, 1, 1, 12, 0, 0)
        mock_state.last_updated = datetime(2023, 1, 1, 12, 0, 1)
        
        mock_hass.states.get.return_value = mock_state
        
        result = await get_entity_state(mock_hass, "light.kitchen")
        
        assert result["entity_id"] == "light.kitchen"
        assert result["state"] == "on"
        assert result["attributes"]["brightness"] == 255
        assert "2023-01-01T12:00:00" in result["last_changed"]
        assert "2023-01-01T12:00:01" in result["last_updated"]
    
    @pytest.mark.asyncio
    async def test_get_entity_state_not_found(self, mock_hass):
        """Test entity not found scenario."""
        mock_hass.states.get.return_value = None
        
        with pytest.raises(HomeAssistantError) as exc_info:
            await get_entity_state(mock_hass, "light.nonexistent")
        
        assert "Entity not found: light.nonexistent" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_entity_state_validation(self, mock_hass):
        """Test entity ID validation."""
        with pytest.raises(ValueError) as exc_info:
            await get_entity_state(mock_hass, "")
        assert "Entity ID is required" in str(exc_info.value)

class TestSetEntityState:
    """Test entity state setting."""
    
    @pytest.mark.asyncio
    async def test_set_entity_state_success(self, mock_hass):
        """Test successful entity state setting."""
        attributes = {"brightness": 128}
        
        result = await set_entity_state(mock_hass, "light.kitchen", "on", attributes)
        
        mock_hass.states.async_set.assert_called_once_with(
            "light.kitchen",
            "on",
            attributes
        )
        
        assert result["status"] == "success"
        assert result["entity_id"] == "light.kitchen"
        assert result["state"] == "on"
        assert result["attributes"] == attributes
    
    @pytest.mark.asyncio
    async def test_set_entity_state_validation(self, mock_hass):
        """Test entity state setting validation."""
        # Test empty entity_id
        with pytest.raises(ValueError) as exc_info:
            await set_entity_state(mock_hass, "", "on")
        assert "Entity ID is required" in str(exc_info.value)
        
        # Test empty state
        with pytest.raises(ValueError) as exc_info:
            await set_entity_state(mock_hass, "light.kitchen", "")
        assert "State value is required" in str(exc_info.value)

class TestToggleEntity:
    """Test entity toggle functionality."""
    
    @pytest.mark.asyncio
    async def test_toggle_entity_success(self, mock_hass):
        """Test successful entity toggle."""
        result = await toggle_entity(mock_hass, "light.kitchen")
        
        mock_hass.services.async_call.assert_called_once_with(
            "light",
            "toggle",
            {"entity_id": "light.kitchen"},
            blocking=True
        )
        
        assert result["status"] == "success"
        assert result["entity_id"] == "light.kitchen"
        assert result["action"] == "toggle"
    
    @pytest.mark.asyncio
    async def test_toggle_entity_validation(self, mock_hass):
        """Test toggle entity validation."""
        with pytest.raises(ValueError) as exc_info:
            await toggle_entity(mock_hass, "")
        assert "Entity ID is required" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_toggle_entity_homeassistant_error(self, mock_hass):
        """Test toggle entity with HomeAssistant error."""
        mock_hass.services.async_call.side_effect = HomeAssistantError("Toggle failed")
        
        with pytest.raises(HomeAssistantError) as exc_info:
            await toggle_entity(mock_hass, "light.kitchen")
        
        assert "Home Assistant error toggling light.kitchen" in str(exc_info.value)

class TestE2EScenarios:
    """End-to-end test scenarios."""
    
    @pytest.mark.asyncio
    async def test_kitchen_lights_e2e_scenario(self, mock_hass):
        """
        E2E test for 'Turn on the kitchen lights' command.
        Mocks the complete workflow from backend response to service call.
        """
        # Setup: Mock Home Assistant context and services
        mock_context = Mock()
        mock_hass.context.Context.return_value = mock_context
        
        # Simulate what would happen when backend processes "Turn on the kitchen lights"
        backend_action_data = {
            "domain": "light",
            "service": "turn_on", 
            "entity_id": "light.kitchen",
            "data": {
                "brightness": 255,
                "color_temp": 370  # Warm white
            }
        }
        
        # Prepare service data as it would be in the dispatch
        service_data = backend_action_data["data"].copy()
        service_data["entity_id"] = backend_action_data["entity_id"]
        
        # Execute the service call
        result = await call_service(
            mock_hass,
            backend_action_data["domain"],
            backend_action_data["service"], 
            service_data
        )
        
        # Verify the complete chain worked
        # 1. Service was called with correct parameters
        mock_hass.services.async_call.assert_called_once_with(
            "light",
            "turn_on",
            {
                "brightness": 255,
                "color_temp": 370,
                "entity_id": "light.kitchen"
            },
            blocking=True,
            context=mock_context
        )
        
        # 2. Thin client origin was set
        mock_hass.context.Context.assert_called_once_with(origin="thin_client")
        
        # 3. Correct response was returned
        expected_response = {
            "status": "success",
            "service": "light.turn_on",
            "data": {
                "brightness": 255,
                "color_temp": 370,
                "entity_id": "light.kitchen"
            },
            "origin": "thin_client"
        }
        
        assert result == expected_response
        
    @pytest.mark.asyncio
    async def test_multiple_lights_e2e_scenario(self, mock_hass):
        """E2E test for controlling multiple lights simultaneously."""
        mock_context = Mock()
        mock_hass.context.Context.return_value = mock_context
        
        # Simulate "Turn on all living room lights"
        service_data = {
            "entity_id": ["light.living_room_1", "light.living_room_2", "light.living_room_3"],
            "brightness": 200
        }
        
        result = await call_service(mock_hass, "light", "turn_on", service_data)
        
        # Verify service call
        mock_hass.services.async_call.assert_called_once_with(
            "light",
            "turn_on",
            service_data,
            blocking=True,
            context=mock_context
        )
        
        # Verify response
        assert result["status"] == "success"
        assert result["service"] == "light.turn_on"
        assert result["data"]["entity_id"] == ["light.living_room_1", "light.living_room_2", "light.living_room_3"]
        assert result["origin"] == "thin_client"

class TestTimeoutAndAsyncBehavior:
    """Test async behavior and timeout handling."""
    
    @pytest.mark.asyncio
    async def test_service_call_respects_timeout(self, mock_hass):
        """Test that service calls respect the 10 second timeout."""
        import time
        
        # Create a slow service call that should timeout
        async def slow_service_call(*args, **kwargs):
            await asyncio.sleep(12)  # Longer than 10s timeout
            
        mock_hass.services.async_call = slow_service_call
        
        start_time = time.time()
        
        with pytest.raises(HomeAssistantError) as exc_info:
            await call_service(mock_hass, "light", "turn_on", {"entity_id": "light.test"})
        
        end_time = time.time()
        
        # Should timeout in approximately 10 seconds, not 12
        assert end_time - start_time < 11  # Allow some buffer
        assert "timed out after 10 seconds" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_concurrent_service_calls(self, mock_hass):
        """Test that multiple service calls can run concurrently."""
        mock_context = Mock()
        mock_hass.context.Context.return_value = mock_context
        
        # Create mock that tracks call order
        call_order = []
        
        async def track_calls(*args, **kwargs):
            call_order.append(args[1])  # Service name
            await asyncio.sleep(0.1)  # Small delay to test concurrency
            
        mock_hass.services.async_call = track_calls
        
        # Run multiple service calls concurrently
        tasks = [
            call_service(mock_hass, "light", "turn_on", {"entity_id": "light.1"}),
            call_service(mock_hass, "light", "turn_off", {"entity_id": "light.2"}),
            call_service(mock_hass, "switch", "toggle", {"entity_id": "switch.1"})
        ]
        
        results = await asyncio.gather(*tasks)
        
        # All should succeed
        assert all(result["status"] == "success" for result in results)
        assert len(call_order) == 3
        assert "turn_on" in call_order
        assert "turn_off" in call_order
        assert "toggle" in call_order
