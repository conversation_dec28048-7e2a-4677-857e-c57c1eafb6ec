#!/usr/bin/env python3
"""
Simple script to validate Gemini API key.
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def validate_api_key():
    """Validate the Gemini API key."""
    api_key = os.getenv("GEMINI_API_KEY")
    
    if not api_key:
        print("❌ No GEMINI_API_KEY found in environment")
        return False
    
    if api_key == "your-gemini-api-key-here":
        print("❌ GEMINI_API_KEY is still the placeholder value")
        print("   Please update .env with your real API key from:")
        print("   https://aistudio.google.com/app/apikey")
        return False
    
    print(f"✅ API key found: {api_key[:8]}...{api_key[-4:]} (masked)")
    
    # Test the API key
    try:
        import google.generativeai as genai
        
        print("🔧 Configuring Gemini...")
        genai.configure(api_key=api_key)
        
        print("🧪 Testing API key with simple request...")
        model = genai.GenerativeModel('gemini-2.0-flash')
        
        # Simple test prompt
        response = model.generate_content("Say 'Hello, API key is working!'")
        
        if response and response.candidates:
            text = response.candidates[0].content.parts[0].text
            print(f"✅ API key is valid! Response: {text}")
            return True
        else:
            print("❌ API key test failed: No response received")
            return False
            
    except Exception as e:
        print(f"❌ API key test failed: {e}")
        
        # Provide helpful error messages
        error_str = str(e).lower()
        if "api key" in error_str and ("invalid" in error_str or "expired" in error_str):
            print("\n💡 Troubleshooting tips:")
            print("   1. Check if your API key is correct")
            print("   2. Generate a new API key at: https://aistudio.google.com/app/apikey")
            print("   3. Make sure the API key has proper permissions")
            print("   4. Check if you have quota/billing enabled")
        elif "quota" in error_str or "limit" in error_str:
            print("\n💡 Quota issue:")
            print("   1. Check your API usage at: https://aistudio.google.com/app/apikey")
            print("   2. You might have exceeded free tier limits")
            print("   3. Consider enabling billing if needed")
        
        return False

if __name__ == "__main__":
    print("🔑 Gemini API Key Validator")
    print("=" * 40)
    
    if validate_api_key():
        print("\n🎉 Your API key is working correctly!")
    else:
        print("\n⚠️  Please fix the API key issue and try again.")
