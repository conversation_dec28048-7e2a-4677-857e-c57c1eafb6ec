import os
import ssl
import secrets
from homeassistant.core import HomeAssistant
from typing import Dict, Any
from urllib.parse import urlparse

def validate_https_url(url: str) -> bool:
    """Validate that the URL is HTTPS and optionally pin SHA-256 cert fingerprint."""
    if not url:
        raise ValueError("Backend URL is not set")
    parsed = urlparse(url)
    if parsed.scheme != "https":
        raise ValueError("Only HTTPS URLs are supported")
    fingerprint = os.getenv("BACKEND_CERT_FINGERPRINT")
    if fingerprint:
        try:
            if not parsed.hostname:
                raise ValueError("URL missing hostname for cert pinning")
            cert = ssl.get_server_certificate((str(parsed.hostname), int(parsed.port) if parsed.port else 443))
            cert_der = ssl.PEM_cert_to_DER_cert(cert)
            import hashlib
            sha256 = hashlib.sha256(cert_der).hexdigest()
            if sha256.lower() != fingerprint.lower():
                raise ValueError("Certificate fingerprint mismatch")
        except Exception as e:
            raise ValueError(f"Certificate pinning failed: {e}")
    return True

def get_jitter_delay(base: float = 1.0, max_jitter: float = 2.0) -> float:
    """Return a random jitter delay using secrets.SystemRandom()."""
    rng = secrets.SystemRandom()
    return base + rng.uniform(0, max_jitter)

def create_automation(hass: HomeAssistant, automation_data: Dict[str, Any]) -> Dict[str, Any]:
    """Create a new automation."""
    if not automation_data.get("action"):
        raise ValueError("Automation action is required")
    
    if not automation_data.get("trigger"):
        raise ValueError("Automation trigger is required")

    # TODO: Implement the actual automation creation logic
    return {
        "status": "success",
        "message": "Automation created successfully"
    }
