Metadata-Version: 2.4
Name: sirohi-thin-client
Version: 0.3.0
Summary: A lightweight, secure client for Home Assistant automation with AI-powered natural language processing via Google Gemini
Author-email: Sirohi <PERSON> <<EMAIL>>
Maintainer-email: Sirohi <PERSON> <<EMAIL>>
License: MIT
Project-URL: Homepage, https://github.com/sirohilabs/thin-client
Project-URL: Documentation, https://docs.sirohilabs.com/thin-client
Project-URL: Repository, https://github.com/sirohilabs/thin-client
Project-URL: Issues, https://github.com/sirohilabs/thin-client/issues
Project-URL: Changelog, https://github.com/sirohilabs/thin-client/blob/main/CHANGELOG.md
Keywords: home-assistant,ai,gemini,automation,smart-home
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Topic :: Home Automation
Classifier: Topic :: Scientific/Engineering :: Artificial Intelligence
Requires-Python: >=3.11
Description-Content-Type: text/markdown
Requires-Dist: fastapi>=0.104.0
Requires-Dist: uvicorn[standard]>=0.24.0
Requires-Dist: pydantic>=2.5.0
Requires-Dist: aiohttp>=3.9.0
Requires-Dist: python-dotenv>=1.0.0
Requires-Dist: google-generativeai>=0.3.0
Provides-Extra: dev
Requires-Dist: pytest>=7.4.0; extra == "dev"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "dev"
Requires-Dist: pytest-cov>=4.1.0; extra == "dev"
Requires-Dist: pytest-mock>=3.12.0; extra == "dev"
Requires-Dist: ruff>=0.1.9; extra == "dev"
Requires-Dist: bandit[toml]>=1.7.5; extra == "dev"
Requires-Dist: mypy>=1.7.0; extra == "dev"
Requires-Dist: pre-commit>=3.6.0; extra == "dev"
Requires-Dist: black>=23.12.0; extra == "dev"
Requires-Dist: isort>=5.13.0; extra == "dev"
Provides-Extra: test
Requires-Dist: pytest>=7.4.0; extra == "test"
Requires-Dist: pytest-asyncio>=0.21.0; extra == "test"
Requires-Dist: pytest-cov>=4.1.0; extra == "test"
Requires-Dist: pytest-mock>=3.12.0; extra == "test"

# Sirohi Thin Client

A lightweight, secure client for Home Assistant automation with AI-powered natural language processing via Google Gemini.

## 🚀 Features

- **Natural Language Processing**: Control your smart home using conversational commands via Google Gemini
- **Secure Communication**: HTTPS with certificate pinning and comprehensive security measures
- **Comprehensive Entity Support**: Lights, switches, sensors, scenes, and automation management
- **Real-time Context**: Dynamic entity state awareness and area-based organization
- **Robust Error Handling**: Graceful degradation with detailed logging and monitoring
- **Production Ready**: Comprehensive testing, security scanning, and deployment guides

## 📋 Requirements

- Python 3.11+
- Home Assistant instance with API access
- Google Gemini API key
- SSL/TLS certificates for production deployment

## 🛠️ Quick Start

### 1. Clone and Setup

```bash
git clone <repository-url>
cd thin-client
pip install -r requirements.txt
```

### 2. Environment Configuration

```bash
# Run the interactive setup
python3 setup_env.py

# Or manually create .env file
cp .env.example .env
# Edit .env with your configuration
```

### 3. Required Environment Variables

```bash
# Gemini API Configuration
GEMINI_API_KEY=your-gemini-api-key-here

# Home Assistant Configuration
HASS_URL=http://your-homeassistant:8123
HASS_TOKEN=your-long-lived-access-token

# Backend Configuration (for production)
BACKEND_URL=https://your-backend-host
BACKEND_CERT_FINGERPRINT=optional-sha256-cert-fingerprint
```

### 4. Get API Keys

**Gemini API Key:**
1. Visit [Google AI Studio](https://aistudio.google.com/app/apikey)
2. Create a new API key
3. Add to your `.env` file

**Home Assistant Token:**
1. Go to Home Assistant → Profile → Long-Lived Access Tokens
2. Create a new token
3. Add to your `.env` file

### 5. Run Tests

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=backend --cov=tests

# Run integration tests (requires real API keys)
INTEGRATION_TESTS=true pytest
```

### 6. Start the Server

```bash
# Development
python3 backend/main.py

# Production (see deployment guide)
uvicorn backend.main:app --host 0.0.0.0 --port 8000
```

## 🏗️ Architecture

```
thin-client/
├── backend/                 # Core backend services
│   ├── main.py             # FastAPI application
│   ├── gemini_client.py    # Gemini AI integration
│   ├── http_utils.py       # HTTP utilities and security
│   └── models.py           # Data models
├── tests/                  # Comprehensive test suite
├── docs/                   # Documentation
├── .env.example           # Environment template
└── requirements.txt       # Dependencies
```

## 🔧 API Endpoints

### Core Endpoints

- `GET /health` - Health check and system status
- `POST /process` - Process natural language commands
- `GET /context` - Get current Home Assistant context
- `POST /entity/{entity_id}/toggle` - Toggle entity state
- `POST /scene/{scene_id}/activate` - Activate scene

### Example Usage

```bash
# Process natural language command
curl -X POST "http://localhost:8000/process" \
  -H "Content-Type: application/json" \
  -d '{
    "prompt": "Turn on the kitchen lights",
    "context": {
      "entities": [{"entity_id": "light.kitchen", "state": "off"}],
      "areas": ["Kitchen"]
    }
  }'

# Get system health
curl "http://localhost:8000/health"
```

## 🔒 Security Features

- **API Key Protection**: Environment-based configuration with .gitignore protection
- **HTTPS Enforcement**: TLS 1.2+ with certificate pinning support
- **Input Validation**: Comprehensive request validation and sanitization
- **Error Redaction**: Sensitive information masked in logs
- **Rate Limiting**: Built-in protection against abuse
- **Security Headers**: CORS, CSP, and other security headers

## 🧪 Testing

The project includes comprehensive testing:

- **Unit Tests**: 78+ tests covering all components
- **Integration Tests**: Real API testing with Gemini
- **Security Tests**: Bandit security scanning
- **Code Quality**: Ruff linting and formatting

```bash
# Run specific test categories
pytest tests/test_api.py -v                    # API tests
pytest backend/test_gemini_client.py -v        # Gemini integration
pytest tests/test_entity_control.py -v         # Entity control

# Run with specific markers
pytest -m "not integration"                    # Skip integration tests
pytest -m "security"                          # Security tests only
```

## 📚 Documentation

- [API Documentation](docs/API.md) - Complete API reference
- [Deployment Guide](docs/DEPLOYMENT.md) - Production deployment
- [Development Guide](docs/DEVELOPMENT.md) - Development setup
- [Security Guide](docs/SECURITY.md) - Security best practices

## 🚀 Deployment

### Development

```bash
python3 backend/main.py
```

### Production

```bash
# Using uvicorn
uvicorn backend.main:app --host 0.0.0.0 --port 8000 --workers 4

# Using Docker (see deployment guide)
docker build -t thin-client .
docker run -p 8000:8000 --env-file .env thin-client
```

## 🔧 Configuration

### Environment Variables

| Variable | Required | Description | Default |
|----------|----------|-------------|---------|
| `GEMINI_API_KEY` | Yes | Google Gemini API key | - |
| `HASS_URL` | Yes | Home Assistant URL | - |
| `HASS_TOKEN` | Yes | HA long-lived token | - |
| `BACKEND_URL` | No | Backend URL for production | - |
| `GEMINI_MODEL` | No | Gemini model to use | `gemini-2.0-flash` |
| `DEBUG` | No | Enable debug logging | `false` |
| `LOG_LEVEL` | No | Logging level | `INFO` |

### Model Configuration

```python
# Customize Gemini settings
GEMINI_MODEL=gemini-2.0-flash
GEMINI_TEMPERATURE=0.1
GEMINI_MAX_OUTPUT_TOKENS=2048
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests: `pytest`
5. Run linting: `ruff check . --fix`
6. Submit a pull request

## 📄 License

[Add your license here]

## 🆘 Support

- **Issues**: [GitHub Issues](link-to-issues)
- **Documentation**: [Project Wiki](link-to-wiki)
- **Security**: Report security issues privately

## 🏆 Acknowledgments

- Google Gemini for AI capabilities
- Home Assistant for smart home integration
- FastAPI for the web framework
- The open-source community

---

**Made with ❤️ by Sirohi Labs**
