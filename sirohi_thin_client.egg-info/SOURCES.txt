README.md
pyproject.toml
setup.py
backend/__init__.py
backend/gemini_client.py
backend/http_utils.py
backend/main.py
backend/models.py
backend/test_gemini_client.py
sirohi_thin_client.egg-info/PKG-INFO
sirohi_thin_client.egg-info/SOURCES.txt
sirohi_thin_client.egg-info/dependency_links.txt
sirohi_thin_client.egg-info/entry_points.txt
sirohi_thin_client.egg-info/requires.txt
sirohi_thin_client.egg-info/top_level.txt
tests/__init__.py
tests/test_api.py
tests/test_automation.py
tests/test_context.py
tests/test_entity_control.py
tests/test_models.py
tests/test_scene.py