#!/usr/bin/env python3
"""
Test script for Gemini integration with environment variables.
Tests both mock and real API scenarios.
"""

import os
import asyncio
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import our Gemini client
from backend.gemini_client import GeminiClient, GeminiConfig, generate_response

async def test_mock_gemini():
    """Test Gemini client with mock responses (no API key needed)."""
    print("🧪 Testing Mock Gemini Client...")
    
    # Create config without real API key
    config = GeminiConfig(api_key="mock-key-for-testing")
    client = GeminiClient(config)
    
    # Test payload
    test_payload = {
        "prompt": "Turn on the kitchen lights",
        "context": {
            "entities": [
                {"entity_id": "light.kitchen", "state": "off"},
                {"entity_id": "light.living_room", "state": "on"}
            ],
            "areas": ["Kitchen", "Living Room"],
            "scenes": [{"name": "Movie Night", "entity_id": "scene.movie_night"}]
        }
    }
    
    try:
        result = await client.generate(test_payload)
        print("✅ Mock generation successful!")
        print(f"   Response: {result['response']}")
        print(f"   Model: {result['metadata']['model']}")
        print(f"   Finish reason: {result['metadata']['finish_reason']}")
        return True
    except Exception as e:
        print(f"❌ Mock generation failed: {e}")
        return False

async def test_real_gemini():
    """Test Gemini client with real API (requires GEMINI_API_KEY)."""
    api_key = os.getenv("GEMINI_API_KEY")
    integration_tests = os.getenv("INTEGRATION_TESTS", "false").lower() == "true"
    
    if not api_key or api_key == "your-gemini-api-key-here":
        print("⏭️  Skipping real API test (no GEMINI_API_KEY set)")
        return True
    
    if not integration_tests:
        print("⏭️  Skipping real API test (INTEGRATION_TESTS=false)")
        return True
    
    print("🌐 Testing Real Gemini API...")
    
    try:
        # Install google-generativeai if not present
        try:
            import google.generativeai as genai
        except ImportError:
            print("📦 Installing google-generativeai...")
            import subprocess
            subprocess.check_call(["pip", "install", "google-generativeai"])
            import google.generativeai as genai
        
        # Create config with real API key
        config = GeminiConfig(
            api_key=api_key,
            model=os.getenv("GEMINI_MODEL", "gemini-2.0-flash-exp"),
            temperature=float(os.getenv("GEMINI_TEMPERATURE", "0.1")),
            max_output_tokens=int(os.getenv("GEMINI_MAX_OUTPUT_TOKENS", "2048"))
        )
        
        # Test payload
        test_payload = {
            "prompt": "Turn on the kitchen lights. Respond with JSON containing 'action' and 'data' fields.",
            "context": {
                "entities": [
                    {"entity_id": "light.kitchen", "state": "off"},
                    {"entity_id": "light.living_room", "state": "on"}
                ],
                "areas": ["Kitchen", "Living Room"]
            }
        }
        
        result = await generate_response(test_payload, config)
        print("✅ Real API generation successful!")
        print(f"   Response: {json.dumps(result['response'], indent=2)}")
        print(f"   Model: {result['metadata']['model']}")
        print(f"   Finish reason: {result['metadata']['finish_reason']}")
        return True
        
    except Exception as e:
        print(f"❌ Real API generation failed: {e}")
        return False

async def test_error_handling():
    """Test error handling scenarios."""
    print("🚨 Testing Error Handling...")
    
    # Test with invalid config
    try:
        config = GeminiConfig(api_key="invalid-key")
        client = GeminiClient(config)
        
        # This should work with mock, but let's test the error path
        print("✅ Error handling setup successful")
        return True
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 Sirohi Thin Client - Gemini Integration Test")
    print("=" * 60)
    
    results = []
    
    # Test mock functionality
    results.append(await test_mock_gemini())
    print()
    
    # Test error handling
    results.append(await test_error_handling())
    print()
    
    # Test real API if configured
    results.append(await test_real_gemini())
    print()
    
    # Summary
    passed = sum(results)
    total = len(results)
    print("📊 Test Summary:")
    print(f"   Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Gemini integration is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
