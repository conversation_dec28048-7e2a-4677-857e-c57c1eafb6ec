import logging
from typing import Dict, Any
from homeassistant.core import HomeAssistant
from homeassistant.exceptions import HomeAssistantError

_LOGGER = logging.getLogger(__name__)

# GOAL:
# Handle automation CRUD operations with proper error handling
# All WS calls wrapped in try/except HomeAssistantError

async def create_automation(hass: HomeAssistant, automation_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create a new automation in Home Assistant.
    Returns automation details on success.
    """
    try:
        # Validate required fields
        if not automation_data.get("alias"):
            raise ValueError("Automation alias is required")
        
        if not automation_data.get("trigger"):
            raise ValueError("Automation trigger is required")
            
        if not automation_data.get("action"):
            raise ValueError("Automation action is required")
        
        # Call Home Assistant automation service
        await hass.services.async_call(
            "automation",
            "reload",
            blocking=True
        )
        
        # Create automation via config entry
        await hass.services.async_call(
            "automation",
            "turn_on",
            {"entity_id": f"automation.{automation_data['alias'].lower().replace(' ', '_')}"},
            blocking=True
        )
        
        _LOGGER.info("Successfully created automation: %s", automation_data.get("alias"))
        return {"status": "created", "automation_id": automation_data.get("alias")}
        
    except HomeAssistantError as e:
        error_msg = f"Home Assistant error creating automation: {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e
        
    except ValueError as e:
        error_msg = f"Invalid automation data: {str(e)}"
        _LOGGER.error(error_msg)
        raise ValueError(error_msg) from e
        
    except Exception as e:
        error_msg = f"Unexpected error creating automation: {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e

async def edit_automation(hass: HomeAssistant, automation_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
    """
    Edit an existing automation in Home Assistant.
    Returns updated automation details on success.
    """
    try:
        if not automation_id or not automation_id.strip():
            raise ValueError("Automation ID is required")
            
        # Check if automation exists
        entity_id = f"automation.{automation_id.lower().replace(' ', '_')}"
        state = hass.states.get(entity_id)
        
        if not state:
            raise HomeAssistantError(f"Automation not found: {automation_id}")
        
        # Apply updates via Home Assistant service
        await hass.services.async_call(
            "automation",
            "reload",
            blocking=True
        )
        
        _LOGGER.info("Successfully updated automation: %s", automation_id)
        return {"status": "updated", "automation_id": automation_id, "updates": updates}
        
    except HomeAssistantError as e:
        error_msg = f"Home Assistant error editing automation '{automation_id}': {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e
        
    except ValueError as e:
        error_msg = f"Invalid input for editing automation: {str(e)}"
        _LOGGER.error(error_msg)
        raise ValueError(error_msg) from e
        
    except Exception as e:
        error_msg = f"Unexpected error editing automation '{automation_id}': {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e

async def delete_automation(hass: HomeAssistant, automation_id: str) -> Dict[str, Any]:
    """
    Delete an automation from Home Assistant.
    Returns deletion confirmation on success.
    """
    try:
        if not automation_id or not automation_id.strip():
            raise ValueError("Automation ID is required")
            
        entity_id = f"automation.{automation_id.lower().replace(' ', '_')}"
        state = hass.states.get(entity_id)
        
        if not state:
            raise HomeAssistantError(f"Automation not found: {automation_id}")
        
        # Delete automation via Home Assistant service
        await hass.services.async_call(
            "automation",
            "turn_off",
            {"entity_id": entity_id},
            blocking=True
        )
        
        # Reload to persist changes
        await hass.services.async_call(
            "automation",
            "reload",
            blocking=True
        )
        
        _LOGGER.info("Successfully deleted automation: %s", automation_id)
        return {"status": "deleted", "automation_id": automation_id}
        
    except HomeAssistantError as e:
        error_msg = f"Home Assistant error deleting automation '{automation_id}': {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e
        
    except ValueError as e:
        error_msg = f"Invalid input for deleting automation: {str(e)}"
        _LOGGER.error(error_msg)
        raise ValueError(error_msg) from e
        
    except Exception as e:
        error_msg = f"Unexpected error deleting automation '{automation_id}': {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e

async def list_automations(hass: HomeAssistant) -> Dict[str, Any]:
    """
    List all automations in Home Assistant.
    Returns list of automation entities.
    """
    try:
        automations = hass.states.async_all("automation")
        
        automation_list = []
        for automation in automations:
            automation_info = {
                "entity_id": automation.entity_id,
                "alias": automation.attributes.get("friendly_name", automation.entity_id),
                "state": automation.state,
                "last_triggered": automation.attributes.get("last_triggered"),
            }
            automation_list.append(automation_info)
        
        _LOGGER.debug("Retrieved %d automations", len(automation_list))
        return {"automations": automation_list, "count": len(automation_list)}
        
    except HomeAssistantError as e:
        error_msg = f"Home Assistant error listing automations: {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e
        
    except Exception as e:
        error_msg = f"Unexpected error listing automations: {str(e)}"
        _LOGGER.error(error_msg)
        raise HomeAssistantError(error_msg) from e
