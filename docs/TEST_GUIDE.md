# Test Guide

Comprehensive testing guide for the Sirohi Thin Client integration.

## Test Environment Setup

### Prerequisites

- Python 3.11+
- Home Assistant test instance
- Google Gemini API key (for integration tests)
- pytest and testing dependencies

### Local Development Setup

```bash
# Clone repository
git clone <repository-url>
cd thin-client

# Create virtual environment
python3 -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# Install dependencies
pip install -r requirements-dev.txt

# Set up environment
cp .env.example .env
# Edit .env with test configuration
```

### Environment Configuration

```bash
# Test Environment Variables (.env)
GEMINI_API_KEY=your-test-api-key
HASS_URL=http://localhost:8123
HASS_TOKEN=your-test-token

# Test Settings
DEBUG=true
LOG_LEVEL=DEBUG
INTEGRATION_TESTS=false  # Set to true for real API testing
PYTEST_TIMEOUT=30
```

## Test Categories

### Unit Tests

Fast, isolated tests with mocked dependencies:

```bash
# Run all unit tests
pytest tests/ -v

# Run specific test modules
pytest tests/test_api.py -v
pytest tests/test_entity_control.py -v
pytest tests/test_context.py -v

# Run with coverage
pytest tests/ --cov=. --cov-report=html
```

### Integration Tests

Tests with real external services:

```bash
# Enable integration tests
export INTEGRATION_TESTS=true

# Run Gemini integration tests
pytest backend/test_gemini_client.py -v

# Run specific integration test
pytest backend/test_gemini_client.py::TestGeminiClient::test_generate_success -v
```

### Security Tests

Security-focused testing:

```bash
# Run security tests only
pytest -m security -v

# Static security analysis
bandit -r . -lll

# Dependency vulnerability scan
safety check
```

### End-to-End Tests

Complete workflow testing:

```bash
# Run E2E tests
pytest -m e2e -v

# Kitchen lights demo
python3 examples/e2e_kitchen_lights_demo.py
```

## Test Commands Reference

### Core Test Commands

```bash
# Quick test run (unit tests only)
pytest -q

# Verbose output with details
pytest -v

# Stop on first failure
pytest -x

# Run tests in parallel
pytest -n auto

# Run specific test pattern
pytest -k "test_entity" -v

# Run tests with markers
pytest -m "not integration" -v
```

### Coverage Commands

```bash
# Generate coverage report
pytest --cov=. --cov-report=html

# Coverage with missing lines
pytest --cov=. --cov-report=term-missing

# Coverage threshold enforcement
pytest --cov=. --cov-fail-under=90
```

### Debugging Commands

```bash
# Run with debugger on failure
pytest --pdb

# Capture output (disable -s for debugging)
pytest -s

# Verbose logging
pytest --log-cli-level=DEBUG

# Run single test with full output
pytest tests/test_api.py::test_health_endpoint -v -s
```

## CI/CD Commands

### GitHub Actions Workflow

```yaml
# .github/workflows/test.yml
name: Test Suite
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-python@v4
        with:
          python-version: '3.11'
      
      - name: Install dependencies
        run: |
          pip install -r requirements-dev.txt
      
      - name: Run tests
        run: |
          pytest -v --cov=. --cov-report=xml
      
      - name: Security scan
        run: |
          bandit -r . -lll
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

### Pre-commit Hooks

```bash
# Install pre-commit hooks
pre-commit install

# Run hooks manually
pre-commit run --all-files

# Update hooks
pre-commit autoupdate
```

## WebSocket Testing Examples

### Manual WebSocket Testing

```python
# Test WebSocket connection
import asyncio
import websockets
import json

async def test_websocket():
    uri = "ws://localhost:8000/ws"
    
    async with websockets.connect(uri) as websocket:
        # Send test message
        message = {
            "type": "process_command",
            "prompt": "Turn on kitchen lights",
            "context": {
                "entities": [
                    {"entity_id": "light.kitchen", "state": "off"}
                ]
            }
        }
        
        await websocket.send(json.dumps(message))
        response = await websocket.recv()
        print(f"Response: {response}")

# Run test
asyncio.run(test_websocket())
```

### WebSocket Test Client

```python
# tests/websocket_client.py
import pytest
import websockets
import json
from unittest.mock import AsyncMock

@pytest.mark.asyncio
async def test_websocket_process_command():
    """Test WebSocket command processing."""
    # Mock WebSocket connection
    mock_websocket = AsyncMock()
    
    # Test message
    message = {
        "type": "process_command",
        "prompt": "Turn on the lights",
        "context": {"entities": []}
    }
    
    # Send message
    await mock_websocket.send(json.dumps(message))
    
    # Verify call
    mock_websocket.send.assert_called_once()
```

## Performance Testing

### Load Testing

```bash
# Install load testing tools
pip install locust

# Run load tests
locust -f tests/load_test.py --host=http://localhost:8000
```

### Performance Benchmarks

```python
# tests/test_performance.py
import time
import pytest

@pytest.mark.performance
def test_api_response_time():
    """Test API response time is under 1 second."""
    start_time = time.time()
    
    # Make API call
    response = client.get("/health")
    
    end_time = time.time()
    response_time = end_time - start_time
    
    assert response_time < 1.0
    assert response.status_code == 200
```

## Test Data Management

### Test Fixtures

```python
# conftest.py
import pytest
from unittest.mock import Mock

@pytest.fixture
def mock_hass():
    """Mock Home Assistant instance."""
    hass = Mock()
    hass.states.get.return_value = Mock(state="off")
    return hass

@pytest.fixture
def sample_entities():
    """Sample entity data for testing."""
    return [
        {"entity_id": "light.kitchen", "state": "off"},
        {"entity_id": "switch.fan", "state": "on"}
    ]
```

### Test Data Files

```bash
# Test data structure
tests/
├── fixtures/
│   ├── entities.json
│   ├── scenes.json
│   └── automations.json
├── mocks/
│   ├── gemini_responses.json
│   └── hass_states.json
└── data/
    ├── valid_payloads.json
    └── invalid_payloads.json
```

## Troubleshooting Tests

### Common Issues

**Import Errors:**
```bash
# Fix Python path issues
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Install in development mode
pip install -e .
```

**Test Discovery Issues:**
```bash
# Check test discovery
pytest --collect-only

# Fix naming conventions
# Files: test_*.py or *_test.py
# Functions: test_*
# Classes: Test*
```

**Async Test Issues:**
```bash
# Install pytest-asyncio
pip install pytest-asyncio

# Mark async tests
@pytest.mark.asyncio
async def test_async_function():
    result = await async_function()
    assert result is not None
```

### Debug Test Failures

```bash
# Run failed tests only
pytest --lf

# Show local variables on failure
pytest --tb=long

# Capture stdout/stderr
pytest --capture=no

# Run with pdb debugger
pytest --pdb-trace
```

## Test Reporting

### Coverage Reports

```bash
# HTML coverage report
pytest --cov=. --cov-report=html
open htmlcov/index.html

# XML coverage for CI
pytest --cov=. --cov-report=xml

# Terminal coverage
pytest --cov=. --cov-report=term
```

### Test Results

```bash
# JUnit XML for CI
pytest --junitxml=test-results.xml

# JSON report
pytest --json-report --json-report-file=test-report.json
```

## Continuous Testing

### Watch Mode

```bash
# Install pytest-watch
pip install pytest-watch

# Run tests on file changes
ptw tests/ --runner "pytest -v"
```

### Test Automation

```bash
# Run tests before commit
git config core.hooksPath .githooks

# Pre-commit test script
#!/bin/bash
pytest -q
if [ $? -ne 0 ]; then
    echo "Tests failed. Commit aborted."
    exit 1
fi
```

---

For more testing information, see:
- [Development Guide](DEVELOPMENT.md)
- [API Documentation](API.md)
- [Security Policy](../SECURITY.md)
