"""Config flow for Sirohi Thin Client integration."""
from __future__ import annotations

import logging
from typing import Any
from urllib.parse import urlparse

import voluptuous as vol
from homeassistant import config_entries
from homeassistant.const import CONF_URL
from homeassistant.core import HomeAssistant
from homeassistant.data_entry_flow import FlowR<PERSON>ult
from homeassistant.exceptions import HomeAssistantError
import aiohttp
import asyncio

from .const import DOMAIN, CONF_LICENSE_KEY

_LOGGER = logging.getLogger(__name__)

STEP_USER_DATA_SCHEMA = vol.Schema(
    {
        vol.Required(CONF_URL): str,
        vol.Required(CONF_LICENSE_KEY): str,
    }
)

async def validate_input(hass: HomeAssistant, data: dict[str, Any]) -> dict[str, Any]:
    """Validate the user input allows us to connect.

    Data has the keys from STEP_USER_DATA_SCHEMA with values provided by the user.
    """
    backend_url = data[CONF_URL].strip()
    license_key = data[CONF_LICENSE_KEY].strip()

    # Validate URL format
    parsed_url = urlparse(backend_url)
    if not parsed_url.scheme or not parsed_url.netloc:
        raise InvalidURL("Invalid URL format")
    
    if parsed_url.scheme != "https":
        raise InvalidURL("Only HTTPS URLs are supported for security")

    # Test connection to backend
    try:
        headers = {
            "Content-Type": "application/json",
            "X-License-Key": license_key,
            "User-Agent": "SirohiThinClient-Config/0.2.0"
        }
        
        test_payload = {
            "action": "test_connection",
            "data": {}
        }

        timeout = aiohttp.ClientTimeout(total=10)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(
                backend_url,
                json=test_payload,
                headers=headers,
                ssl=True
            ) as response:
                if response.status == 401:
                    raise InvalidLicense("Invalid license key")
                elif response.status >= 400:
                    error_text = await response.text()
                    raise CannotConnect(f"Backend error: {response.status} - {error_text}")
                
                # Connection successful
                _LOGGER.info("Successfully validated connection to Sirohi backend")

    except aiohttp.ClientError as err:
        _LOGGER.error("Failed to connect to backend: %s", err)
        raise CannotConnect(f"Connection failed: {err}")
    except asyncio.TimeoutError:
        _LOGGER.error("Connection to backend timed out")
        raise CannotConnect("Connection timed out after 10 seconds")

    # Return info that you want to store in the config entry.
    return {
        "title": f"Sirohi Thin Client ({parsed_url.netloc})",
        "backend_url": backend_url,
        "license_key": license_key
    }


class ConfigFlow(config_entries.ConfigFlow, domain=DOMAIN):
    """Handle a config flow for Sirohi Thin Client."""

    VERSION = 1

    async def async_step_user(
        self, user_input: dict[str, Any] | None = None
    ) -> FlowResult:
        """Handle the initial step."""
        errors: dict[str, str] = {}
        
        if user_input is not None:
            try:
                info = await validate_input(self.hass, user_input)
            except CannotConnect:
                errors["base"] = "cannot_connect"
            except InvalidURL:
                errors[CONF_URL] = "invalid_url"
            except InvalidLicense:
                errors[CONF_LICENSE_KEY] = "invalid_license"
            except Exception:  # pylint: disable=broad-except
                _LOGGER.exception("Unexpected exception")
                errors["base"] = "unknown"
            else:
                # Check if already configured
                await self.async_set_unique_id(f"sirohi_{info['backend_url']}")
                self._abort_if_unique_id_configured()
                
                return self.async_create_entry(
                    title=info["title"],
                    data={
                        CONF_URL: info["backend_url"],
                        CONF_LICENSE_KEY: info["license_key"]
                    }
                )

        return self.async_show_form(
            step_id="user",
            data_schema=STEP_USER_DATA_SCHEMA,
            errors=errors,
            description_placeholders={
                "docs_url": "https://tea.griffin.ovh/g/thin-client",
            }
        )


class CannotConnect(HomeAssistantError):
    """Error to indicate we cannot connect."""


class InvalidURL(HomeAssistantError):
    """Error to indicate the URL is invalid."""


class InvalidLicense(HomeAssistantError):
    """Error to indicate the license key is invalid."""
