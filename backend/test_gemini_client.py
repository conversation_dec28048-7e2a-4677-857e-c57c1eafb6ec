"""Unit tests for Gemini client integration."""
import pytest
from unittest.mock import Mo<PERSON>, patch, AsyncMock
from google.api_core import exceptions as google_exceptions

from backend.gemini_client import (
    GeminiClient,
    GeminiConfig,
    RetryableG<PERSON>iniError,
    <PERSON><PERSON>G<PERSON>iniError,
    GeminiError,
    generate_response
)

@pytest.fixture
def mock_config():
    """Mock Gemini configuration."""
    return GeminiConfig(api_key="test-api-key")

@pytest.fixture
def mock_gemini_response():
    """Mock successful Gemini response."""
    mock_response = Mock()
    mock_response.candidates = [Mock()]
    mock_response.candidates[0].finish_reason = Mock()
    mock_response.candidates[0].finish_reason.name = "STOP"
    mock_response.candidates[0].content = Mock()
    mock_response.candidates[0].content.parts = [Mock()]
    mock_response.candidates[0].content.parts[0].text = '{"action": "call_service", "data": {"domain": "light", "service": "turn_on", "entity_id": "light.kitchen"}}'
    mock_response.candidates[0].safety_ratings = []
    return mock_response

@pytest.fixture
def sample_prompt_json():
    """Sample prompt JSON for testing."""
    return {
        "prompt": "Turn on the kitchen lights",
        "context": {
            "entities": [
                {"entity_id": "light.kitchen", "state": "off"},
                {"entity_id": "light.living_room", "state": "on"}
            ],
            "areas": ["Kitchen", "Living Room"],
            "scenes": [{"name": "Movie Night", "entity_id": "scene.movie_night"}]
        }
    }

class TestGeminiClient:
    """Test cases for GeminiClient."""

    def test_init_with_config(self, mock_config):
        """Test client initialization with provided config."""
        with patch('backend.gemini_client.genai') as mock_genai:
            client = GeminiClient(mock_config)
            assert client.config == mock_config
            mock_genai.configure.assert_called_once_with(api_key="test-api-key")

    def test_init_without_config_with_env_var(self):
        """Test client initialization without config but with env var."""
        with patch('backend.gemini_client.os.getenv', return_value="env-api-key"), \
             patch('backend.gemini_client.genai') as mock_genai:
            client = GeminiClient()
            assert client.config.api_key == "env-api-key"
            mock_genai.configure.assert_called_once_with(api_key="env-api-key")

    def test_init_without_config_no_env_var(self):
        """Test client initialization fails without config or env var."""
        with patch('backend.gemini_client.os.getenv', return_value=None):
            with pytest.raises(ValueError, match="GEMINI_API_KEY environment variable is required"):
                GeminiClient()

    @pytest.mark.asyncio
    async def test_generate_success(self, mock_config, mock_gemini_response, sample_prompt_json):
        """Test successful generation."""
        with patch('backend.gemini_client.genai') as mock_genai:
            client = GeminiClient(mock_config)
            
            # Mock the executor call
            with patch('asyncio.get_event_loop') as mock_loop:
                mock_loop.return_value.run_in_executor = AsyncMock(return_value=mock_gemini_response)
                
                result = await client.generate(sample_prompt_json)
                
                assert result["response"]["action"] == "call_service"
                assert result["response"]["data"]["domain"] == "light"
                assert result["metadata"]["model"] == mock_config.model_name
                assert result["metadata"]["finish_reason"] == "STOP"

    @pytest.mark.asyncio
    async def test_generate_rate_limit_error(self, mock_config, sample_prompt_json):
        """Test handling of rate limit errors (429)."""
        with patch('backend.gemini_client.genai'):
            client = GeminiClient(mock_config)
            
            # Create rate limit exception
            rate_limit_error = google_exceptions.ResourceExhausted("Rate limit exceeded")
            
            with patch('asyncio.get_event_loop') as mock_loop:
                mock_loop.return_value.run_in_executor = AsyncMock(side_effect=rate_limit_error)
                
                with pytest.raises(RetryableGeminiError) as exc_info:
                    await client.generate(sample_prompt_json)
                
                assert exc_info.value.status_code == 429
                assert exc_info.value.retry_after == 60  # Default retry after

    @pytest.mark.asyncio
    async def test_generate_server_error(self, mock_config, sample_prompt_json):
        """Test handling of server errors (5xx)."""
        with patch('backend.gemini_client.genai'):
            client = GeminiClient(mock_config)
            
            # Create server error
            server_error = google_exceptions.ServerError("Internal server error")
            
            with patch('asyncio.get_event_loop') as mock_loop:
                mock_loop.return_value.run_in_executor = AsyncMock(side_effect=server_error)
                
                with pytest.raises(RetryableGeminiError) as exc_info:
                    await client.generate(sample_prompt_json)
                
                assert exc_info.value.status_code == 500

    @pytest.mark.asyncio
    async def test_generate_client_error(self, mock_config, sample_prompt_json):
        """Test handling of client errors (4xx)."""
        with patch('backend.gemini_client.genai'):
            client = GeminiClient(mock_config)
            
            # Create client error
            client_error = google_exceptions.ClientError("Bad request")
            client_error.code = 400
            
            with patch('asyncio.get_event_loop') as mock_loop:
                mock_loop.return_value.run_in_executor = AsyncMock(side_effect=client_error)
                
                with pytest.raises(FatalGeminiError) as exc_info:
                    await client.generate(sample_prompt_json)
                
                assert exc_info.value.status_code == 400

    @pytest.mark.asyncio
    async def test_generate_safety_block(self, mock_config, sample_prompt_json):
        """Test handling of safety-blocked content."""
        with patch('backend.gemini_client.genai'):
            client = GeminiClient(mock_config)
            
            # Create response with safety block
            mock_response = Mock()
            mock_response.candidates = [Mock()]
            mock_response.candidates[0].finish_reason = Mock()
            mock_response.candidates[0].finish_reason.name = "SAFETY"
            mock_response.candidates[0].content = Mock()
            mock_response.candidates[0].content.parts = []
            mock_response.candidates[0].safety_ratings = []
            
            with patch('asyncio.get_event_loop') as mock_loop:
                mock_loop.return_value.run_in_executor = AsyncMock(return_value=mock_response)
                
                with pytest.raises(FatalGeminiError) as exc_info:
                    await client.generate(sample_prompt_json)
                
                assert "Content blocked by safety filters" in str(exc_info.value)
                assert exc_info.value.status_code == 400

    @pytest.mark.asyncio
    async def test_generate_no_candidates(self, mock_config, sample_prompt_json):
        """Test handling of response with no candidates."""
        with patch('backend.gemini_client.genai'):
            client = GeminiClient(mock_config)
            
            # Create response with no candidates
            mock_response = Mock()
            mock_response.candidates = []
            
            with patch('asyncio.get_event_loop') as mock_loop:
                mock_loop.return_value.run_in_executor = AsyncMock(return_value=mock_response)
                
                with pytest.raises(GeminiError, match="No candidates returned from Gemini"):
                    await client.generate(sample_prompt_json)

    @pytest.mark.asyncio
    async def test_generate_non_json_response(self, mock_config, sample_prompt_json):
        """Test handling of non-JSON responses."""
        with patch('backend.gemini_client.genai'):
            client = GeminiClient(mock_config)
            
            # Create response with non-JSON text
            mock_response = Mock()
            mock_response.candidates = [Mock()]
            mock_response.candidates[0].finish_reason = Mock()
            mock_response.candidates[0].finish_reason.name = "STOP"
            mock_response.candidates[0].content = Mock()
            mock_response.candidates[0].content.parts = [Mock()]
            mock_response.candidates[0].content.parts[0].text = "This is plain text, not JSON"
            mock_response.candidates[0].safety_ratings = []
            
            with patch('asyncio.get_event_loop') as mock_loop:
                mock_loop.return_value.run_in_executor = AsyncMock(return_value=mock_response)
                
                result = await client.generate(sample_prompt_json)
                
                # Should wrap non-JSON in text_response format
                assert result["response"]["action"] == "text_response"
                assert result["response"]["data"]["text"] == "This is plain text, not JSON"

    def test_build_prompt_from_json(self, mock_config, sample_prompt_json):
        """Test prompt building from JSON input."""
        with patch('backend.gemini_client.genai'):
            client = GeminiClient(mock_config)
            
            prompt = client._build_prompt_from_json(sample_prompt_json)
            
            assert "Turn on the kitchen lights" in prompt
            assert "light.kitchen: off" in prompt
            assert "light.living_room: on" in prompt
            assert "Kitchen, Living Room" in prompt
            assert "Movie Night" in prompt
            assert "respond with JSON" in prompt

    def test_extract_retry_after_with_header(self, mock_config):
        """Test extracting retry-after from exception."""
        with patch('backend.gemini_client.genai'):
            client = GeminiClient(mock_config)
            
            # Mock exception with retry-after header
            mock_exception = Mock()
            mock_exception.response = Mock()
            mock_exception.response.headers = {"retry-after": "120"}
            
            retry_after = client._extract_retry_after(mock_exception)
            assert retry_after == 120

    def test_extract_retry_after_without_header(self, mock_config):
        """Test extracting retry-after without header returns default."""
        with patch('backend.gemini_client.genai'):
            client = GeminiClient(mock_config)
            
            # Mock exception without retry-after header
            mock_exception = Mock()
            mock_exception.response = None
            
            retry_after = client._extract_retry_after(mock_exception)
            assert retry_after == 60  # Default

class TestGeminiHTTPIntegration:
    """Test HTTP integration with mocked responses."""

    @pytest.mark.asyncio
    async def test_http_200_flow(self):
        """Test successful HTTP 200 response flow."""
        # This would typically use pytest-httpx, but we'll mock for simplicity
        mock_response_data = {
            "response": {
                "action": "call_service",
                "data": {
                    "domain": "light",
                    "service": "turn_on",
                    "entity_id": "light.kitchen"
                }
            },
            "metadata": {
                "model": "gemini-2.0-flash-exp",
                "finish_reason": "STOP"
            }
        }
        
        with patch('backend.gemini_client.get_client') as mock_get_client:
            mock_client = Mock()
            mock_client.generate = AsyncMock(return_value=mock_response_data)
            mock_get_client.return_value = mock_client
            
            result = await generate_response({
                "prompt": "Turn on kitchen lights",
                "context": {}
            })
            
            assert result == mock_response_data
            mock_client.generate.assert_called_once()

    @pytest.mark.asyncio
    async def test_http_429_flow(self):
        """Test HTTP 429 rate limit response flow."""
        with patch('backend.gemini_client.get_client') as mock_get_client:
            mock_client = Mock()
            mock_client.generate = AsyncMock(
                side_effect=RetryableGeminiError("Rate limit", 429, 60)
            )
            mock_get_client.return_value = mock_client
            
            with pytest.raises(RetryableGeminiError) as exc_info:
                await generate_response({
                    "prompt": "Turn on kitchen lights",
                    "context": {}
                })
            
            assert exc_info.value.status_code == 429
            assert exc_info.value.retry_after == 60

class TestGeminiConfiguration:
    """Test various configuration scenarios."""

    def test_custom_model_config(self):
        """Test configuration with custom model."""
        config = GeminiConfig(
            api_key="test-key",
            model_name="gemini-1.5-pro",
            temperature=0.5,
            max_output_tokens=4096
        )
        
        with patch('backend.gemini_client.genai') as mock_genai:
            client = GeminiClient(config)
            
            # Verify model was initialized with custom config
            mock_genai.GenerativeModel.assert_called_once()
            call_args = mock_genai.GenerativeModel.call_args
            
            # Check if called with positional or keyword arguments
            if call_args[0]:  # positional args
                assert call_args[0][0] == "gemini-1.5-pro"
            else:  # keyword args
                assert call_args[1]["model_name"] == "gemini-1.5-pro"
            
            # Check generation config
            gen_config = call_args[1]["generation_config"]
            assert gen_config["temperature"] == 0.5
            assert gen_config["max_output_tokens"] == 4096

    def test_safety_settings_applied(self, mock_config):
        """Test that safety settings are properly applied."""
        with patch('backend.gemini_client.genai') as mock_genai:
            client = GeminiClient(mock_config)
            
            call_args = mock_genai.GenerativeModel.call_args
            safety_settings = call_args[1]["safety_settings"]
            
            # Verify all harm categories are configured
            from google.generativeai.types import HarmCategory
            expected_categories = [
                HarmCategory.HARM_CATEGORY_HATE_SPEECH,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT,
                HarmCategory.HARM_CATEGORY_HARASSMENT,
            ]
            
            for category in expected_categories:
                assert category in safety_settings

if __name__ == "__main__":
    pytest.main([__file__])
