import aiohttp
import asyncio
import logging

_LOGGER = logging.getLogger(__name__)

async def _http_post_with_retry(url, payload, headers, max_retries=3, base_delay=1.0):
    for attempt in range(max_retries):
        try:
            timeout = aiohttp.ClientTimeout(total=30)
            async with aiohttp.ClientSession(timeout=timeout) as session:
                async with session.post(url, json=payload, headers=headers, ssl=True) as response:
                    if response.status == 200:
                        return await response.json()
                    elif response.status == 429:
                        retry_after = int(response.headers.get('Retry-After', base_delay))
                        _LOGGER.warning("Rate limited, retrying after %d seconds", retry_after)
                        await asyncio.sleep(retry_after)
                        continue
                    else:
                        error_text = await response.text()
                        raise Exception(f"POST {url} returned {response.status}: {error_text}")
        except Exception as e:
            if attempt == max_retries - 1:
                _LOGGER.error("POST failed after %d attempts: %s", max_retries, str(e))
                raise
            await asyncio.sleep(base_delay * (attempt + 1))
