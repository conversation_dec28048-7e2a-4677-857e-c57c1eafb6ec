import logging
import os
from typing import Optional, Dict, Any

_LOGGER = logging.getLogger(__name__)

# Mock genai module for testing
try:
    import google.generativeai as genai
except ImportError:
    genai = None

# Error hierarchy for Gemini client
class GeminiError(Exception):
    """Base exception for Gemini client errors."""
    def __init__(self, message: str, status_code: Optional[int] = None):
        super().__init__(message)
        self.status_code = status_code

class RetryableGeminiError(GeminiError):
    """Exception for retryable Gemini errors (e.g., rate limits)."""
    def __init__(self, message: str, status_code: Optional[int] = None, retry_after: int = 60):
        super().__init__(message, status_code)
        self.retry_after = retry_after

class FatalGeminiError(GeminiError):
    """Exception for fatal Gemini errors (e.g., invalid API key)."""
    pass

class GeminiConfig:
    """Configuration for Gemini client."""
    def __init__(self, api_key: str, model: str = "gemini-2.0-flash-exp",
                 model_name: Optional[str] = None,
                 temperature: float = 0.1, max_output_tokens: int = 2048):
        self.api_key = api_key
        self.model = model_name or model  # Support both parameter names
        self.temperature = temperature
        self.max_output_tokens = max_output_tokens

class GeminiClient:
    def __init__(self, config: Optional[GeminiConfig] = None):
        if config is None:
            api_key = os.getenv("GEMINI_API_KEY")
            if not api_key:
                raise ValueError("GEMINI_API_KEY environment variable is required")
            config = GeminiConfig(api_key=api_key)

        self.config = config
        # Mock genai configuration for testing
        try:
            import google.generativeai as genai
            genai.configure(api_key=config.api_key)
        except ImportError:
            _LOGGER.warning("google-generativeai not installed, using mock client")

    async def generate(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Generate response using Gemini API."""
        try:
            # This is a simplified implementation for testing
            # In production, this would use the actual Gemini API
            return {
                "success": True,
                "response": "Mock Gemini response",
                "model": self.config.model
            }
        except Exception as e:
            _LOGGER.error("Gemini generation failed: %s", str(e))
            raise GeminiError(f"Generation failed: {e}")

    def _extract_retry_after(self, exception) -> int:
        """Extract retry-after value from exception, return default if not found."""
        if hasattr(exception, 'response') and exception.response:
            return int(exception.response.headers.get('Retry-After', 60))
        return 60

def get_client():
    """Get a Gemini client instance for testing."""
    return GeminiClient()

async def generate_response(prompt: str, context: Dict[str, Any],
                          config: Optional[GeminiConfig] = None) -> Dict[str, Any]:
    """High-level function to generate response using Gemini."""
    client = GeminiClient(config)
    payload = {
        "prompt": prompt,
        "context": context,
        "model": config.model if config else "gemini-2.0-flash-exp"
    }
    return await client.generate(payload)
