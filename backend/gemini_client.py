import logging
from .http_utils import _http_post_with_retry

_LOGGER = logging.getLogger(__name__)

class GeminiClient:
    def __init__(self, backend_url, license_key):
        self.backend_url = backend_url
        self.license_key = license_key

    async def generate(self, payload):
        headers = {
            "Content-Type": "application/json",
            "X-License-Key": self.license_key,
            "User-Agent": "GeminiClient/1.0"
        }
        return await _http_post_with_retry(self.backend_url, payload, headers)
