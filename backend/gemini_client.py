import logging
import os
from typing import Optional, Dict, Any

_LOGGER = logging.getLogger(__name__)

# Mock genai module for testing
try:
    import google.generativeai as genai
except ImportError:
    genai = None

# Error hierarchy for Gemini client
class GeminiError(Exception):
    """Base exception for Gemini client errors."""
    def __init__(self, message: str, status_code: Optional[int] = None):
        super().__init__(message)
        self.status_code = status_code

class RetryableGeminiError(GeminiError):
    """Exception for retryable Gemini errors (e.g., rate limits)."""
    def __init__(self, message: str, status_code: Optional[int] = None, retry_after: int = 60):
        super().__init__(message, status_code)
        self.retry_after = retry_after

class FatalGeminiError(GeminiError):
    """Exception for fatal Gemini errors (e.g., invalid API key)."""
    pass

class GeminiConfig:
    """Configuration for Gemini client."""
    def __init__(self, api_key: str, model: str = "gemini-2.0-flash",
                 model_name: Optional[str] = None,
                 temperature: float = 0.1, max_output_tokens: int = 2048):
        self.api_key = api_key
        self.model = model_name or model  # Support both parameter names
        self.model_name = self.model  # Alias for compatibility
        self.temperature = temperature
        self.max_output_tokens = max_output_tokens

class GeminiClient:
    def __init__(self, config: Optional[GeminiConfig] = None):
        if config is None:
            api_key = os.getenv("GEMINI_API_KEY")
            if not api_key:
                raise ValueError("GEMINI_API_KEY environment variable is required")
            config = GeminiConfig(api_key=api_key)

        self.config = config
        # Configure genai (will be mocked in tests)
        if genai is not None:
            genai.configure(api_key=config.api_key)
        else:
            _LOGGER.warning("google-generativeai not installed, using mock client")

    async def generate(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Generate response using Gemini API."""
        import asyncio

        try:
            prompt = payload.get("prompt", "")
            context = payload.get("context", {})

            # Build the prompt from JSON
            built_prompt = self._build_prompt_from_json({"prompt": prompt, "context": context})

            # Use asyncio executor to run the sync Gemini API call
            loop = asyncio.get_event_loop()
            gemini_response = await loop.run_in_executor(None, self._call_gemini_sync, built_prompt)

            # Parse the response
            response_data = self._parse_gemini_response(gemini_response)

            return {
                "success": True,
                "response": response_data,
                "prompt": built_prompt,
                "metadata": {
                    "model": self.config.model,
                    "finish_reason": getattr(gemini_response.candidates[0].finish_reason, 'name', 'STOP') if gemini_response.candidates else 'UNKNOWN',
                    "safety_ratings": [rating for rating in getattr(gemini_response.candidates[0], 'safety_ratings', [])] if gemini_response.candidates else []
                }
            }
        except Exception as e:
            _LOGGER.error("Gemini generation failed: %s", str(e))
            # Handle Google API exceptions
            try:
                from google.api_core import exceptions as google_exceptions
                if isinstance(e, google_exceptions.ResourceExhausted):
                    raise RetryableGeminiError(f"Rate limited: {e}", 429, self._extract_retry_after(e))
                elif isinstance(e, google_exceptions.InternalServerError):
                    raise RetryableGeminiError(f"Server error: {e}", 500)
                elif isinstance(e, google_exceptions.Unauthenticated):
                    raise FatalGeminiError(f"Authentication failed: {e}", 401)
                elif isinstance(e, google_exceptions.InvalidArgument):
                    raise FatalGeminiError(f"Invalid request: {e}", 400)
                elif isinstance(e, google_exceptions.ClientError):
                    raise FatalGeminiError(f"Client error: {e}", getattr(e, 'code', 400))
            except ImportError:
                # Fallback to string matching if google.api_core not available
                pass

            # Determine error type based on exception string
            error_str = str(e).lower()
            if "rate limit" in error_str or "429" in error_str or "resourceexhausted" in error_str:
                raise RetryableGeminiError(f"Rate limited: {e}", 429, self._extract_retry_after(e))
            elif "server error" in error_str or "500" in error_str or "internalservererror" in error_str:
                raise RetryableGeminiError(f"Server error: {e}", 500)
            elif "unauthorized" in error_str or "401" in error_str or "unauthenticated" in error_str:
                raise FatalGeminiError(f"Authentication failed: {e}", 401)
            elif "safety" in error_str:
                raise FatalGeminiError(f"Safety block: {e}")
            else:
                raise GeminiError(f"Generation failed: {e}")

    def _call_gemini_sync(self, prompt: str):
        """Synchronous call to Gemini API (for use with executor)."""
        # Check if we should use mock (for testing or invalid API keys)
        use_mock = (
            genai is None or
            self.config.api_key in ["mock-key-for-testing", "test-api-key", "your-gemini-api-key-here"] or
            os.getenv("USE_MOCK_GEMINI", "false").lower() == "true"
        )

        if use_mock:
            # Mock response for testing
            from unittest.mock import Mock
            mock_response = Mock()
            mock_response.candidates = [Mock()]
            mock_response.candidates[0].finish_reason = Mock()
            mock_response.candidates[0].finish_reason.name = "STOP"
            mock_response.candidates[0].content = Mock()
            mock_response.candidates[0].content.parts = [Mock()]
            mock_response.candidates[0].content.parts[0].text = '{"action": "call_service", "data": {"domain": "light", "service": "turn_on", "entity_id": "light.kitchen"}}'
            mock_response.candidates[0].safety_ratings = []
            return mock_response

        # Real Gemini API call
        if genai is not None:
            model = genai.GenerativeModel(
                model_name=self.config.model,
                generation_config=genai.types.GenerationConfig(
                    temperature=self.config.temperature,
                    max_output_tokens=self.config.max_output_tokens,
                )
            )
            return model.generate_content(prompt)
        else:
            raise RuntimeError("google.generativeai not available for real API calls")

    def _parse_gemini_response(self, response) -> Dict[str, Any]:
        """Parse Gemini response into structured data."""
        import json

        if not response.candidates:
            raise GeminiError("No candidates in response")

        candidate = response.candidates[0]

        # Check finish reason
        finish_reason = getattr(candidate.finish_reason, 'name', 'UNKNOWN')
        if finish_reason == 'SAFETY':
            raise FatalGeminiError("Response blocked by safety filters")
        elif finish_reason not in ['STOP', 'MAX_TOKENS']:
            raise GeminiError(f"Unexpected finish reason: {finish_reason}")

        # Extract text content
        if not candidate.content or not candidate.content.parts:
            raise GeminiError("No content in response")

        text_content = candidate.content.parts[0].text

        # Try to parse as JSON
        try:
            return json.loads(text_content)
        except json.JSONDecodeError:
            # Return as text response if not valid JSON
            return {"action": "text_response", "text": text_content}

    def _build_prompt_from_json(self, json_data: Dict[str, Any]) -> str:
        """Build a prompt string from JSON data."""
        prompt = json_data.get("prompt", "")
        context = json_data.get("context", {})

        # Build comprehensive context string
        context_parts = []

        # Add entities with their states
        entities = context.get("entities", [])
        if entities:
            entity_states = []
            for entity in entities:
                entity_id = entity.get("entity_id", "")
                state = entity.get("state", "")
                entity_states.append(f"{entity_id}: {state}")
            context_parts.append(f"Available entities: {', '.join(entity_states)}")

        # Add areas
        areas = context.get("areas", [])
        if areas:
            context_parts.append(f"Available areas: {', '.join(areas)}")

        # Add scenes
        scenes = context.get("scenes", [])
        if scenes:
            scene_names = [scene.get("name", "") for scene in scenes]
            context_parts.append(f"Available scenes: {', '.join(scene_names)}")

        # Build final prompt
        context_str = ". ".join(context_parts)
        if context_str:
            context_str += ". "

        # Add instruction to respond with JSON
        instruction = "Please respond with JSON containing an 'action' field and appropriate 'data' field."

        return f"{context_str}{prompt}. {instruction}"

    def _extract_retry_after(self, exception) -> int:
        """Extract retry-after value from exception, return default if not found."""
        if hasattr(exception, 'response') and exception.response and hasattr(exception.response, 'headers'):
            headers = exception.response.headers
            # Try both common header formats
            retry_after = headers.get('retry-after') or headers.get('Retry-After')
            if retry_after:
                return int(retry_after)
        return 60

def get_client():
    """Get a Gemini client instance for testing."""
    return GeminiClient()

async def generate_response(payload: Dict[str, Any],
                          config: Optional[GeminiConfig] = None) -> Dict[str, Any]:
    """High-level function to generate response using Gemini."""
    client = get_client() if config is None else GeminiClient(config)
    return await client.generate(payload)
