# Security Policy

## Threat Model

The Sirohi Thin Client operates in a three-tier architecture with specific security boundaries:

```
Home Assistant ←→ Thin Client ←→ Backend ←→ Gemini API
```

### Trust Boundaries

1. **Home Assistant ↔ Thin Client**: Local network communication with API token authentication
2. **Thin Client ↔ Backend**: HTTPS with certificate pinning and origin validation
3. **Backend ↔ Gemini API**: HTTPS with API key authentication and rate limiting

### Threat Vectors

| Threat                  | Mitigation                        | Implementation                       |
| ----------------------- | --------------------------------- | ------------------------------------ |
| **Man-in-the-Middle**   | HTTPS + Certificate Pinning       | `http_utils.py:validate_https_url()` |
| **API Key Exposure**    | Environment variables + Redaction | `context.py:redact_sensitive_data()` |
| **Service Abuse**       | Timeouts + Rate Limiting          | `entity_control.py:call_service()`   |
| **Injection Attacks**   | JSON Schema Validation            | `schemas/thin_client_v0.2.json`      |
| **Information Leakage** | Structured Error Handling         | `api.py:handle_exceptions()`         |

## Security Features

### 🔒 Authentication & Authorization

- **API Token Authentication**: Home Assistant long-lived access tokens
- **Environment-based Secrets**: No hardcoded credentials
- **Automatic Key Redaction**: Sensitive data masked in logs and responses

### 🛡️ Network Security

- **HTTPS Enforcement**: All external communications use TLS 1.2+
- **Certificate Pinning**: SHA-256 fingerprint validation for backend connections
- **Origin Validation**: WebSocket connections validated against allowlist
- **Timeout Protection**: 10s service calls, 60s AI generation limits

### 🔍 Input Validation

- **JSON Schema Validation**: Strict schema enforcement (v0.2)
- **Pydantic Models**: Type-safe data validation
- **Entity ID Validation**: Home Assistant entity format verification
- **Service Parameter Sanitization**: Domain/service name validation

### 📊 Monitoring & Logging

- **Structured Error Handling**: No sensitive information in error messages
- **Comprehensive Logging**: Security events tracked with context
- **Origin Tracking**: All service calls tagged with `thin_client` origin
- **Rate Limit Monitoring**: API usage tracking and alerting

### 🚨 Error Handling

- **Graceful Degradation**: Service failures don't expose system internals
- **Sanitized Responses**: Error messages scrubbed of sensitive data
- **Exception Hierarchy**: Structured error types for proper handling
- **Timeout Recovery**: Automatic recovery from network timeouts

## Implementation Details

### Certificate Pinning

```python
# Environment variable configuration
BACKEND_CERT_FINGERPRINT=sha256:AB:CD:EF:...

# Validation in http_utils.py
def validate_certificate_pinning(url: str, expected_fingerprint: str) -> bool:
    """Validate SSL certificate fingerprint against expected value."""
```

### Key Redaction

```python
# Automatic redaction in context.py
REDACTION_PATTERNS = [
    r'Bearer\s+[A-Za-z0-9._-]+',  # Authorization headers
    r'[A-Za-z0-9]{32,}',          # API keys (32+ chars)
    r'X-License-Key:\s*[^\s]+',   # License headers
]
```

### Origin Validation

```python
# WebSocket origin checking
ALLOWED_ORIGINS = [
    "http://localhost:8123",
    "https://homeassistant.local",
    # Additional origins from config
]
```

## Security Testing

### Automated Security Scans

- **Bandit**: Static security analysis for Python code
- **Safety**: Dependency vulnerability scanning
- **Semgrep**: Custom security rules for Home Assistant integrations

### Test Coverage

| Component              | Coverage | Security Tests                            |
| ---------------------- | -------- | ----------------------------------------- |
| **API Endpoints**      | 95%      | Input validation, auth bypass, injection  |
| **Gemini Client**      | 92%      | API key handling, rate limiting, timeouts |
| **Entity Control**     | 88%      | Service injection, timeout protection     |
| **Context Collection** | 90%      | Data redaction, sensitive info leakage    |

### Manual Security Testing

- **Penetration Testing**: Regular security assessments
- **Code Review**: Security-focused code reviews for all changes
- **Dependency Audits**: Regular review of third-party dependencies

## Compliance & Standards

### Security Standards

- **OWASP Top 10**: All applicable threats mitigated
- **NIST Cybersecurity Framework**: Identify, Protect, Detect, Respond, Recover
- **Home Assistant Security Guidelines**: Integration security best practices

### Privacy Protection

- **Data Minimization**: Only necessary data collected and processed
- **Retention Limits**: Logs rotated, temporary data cleared
- **User Consent**: Clear disclosure of data usage and AI processing

## Incident Response

### Security Incident Classification

- **P0 - Critical**: API key exposure, authentication bypass
- **P1 - High**: Service abuse, data leakage, injection attacks
- **P2 - Medium**: Timeout issues, rate limit bypass
- **P3 - Low**: Information disclosure, logging issues

### Response Procedures

1. **Detection**: Automated monitoring and manual reporting
2. **Assessment**: Impact analysis and threat classification
3. **Containment**: Immediate mitigation and service isolation
4. **Recovery**: System restoration and security hardening
5. **Lessons Learned**: Post-incident review and improvements

## Reporting Vulnerabilities

### Responsible Disclosure

Please report security vulnerabilities through:

- **Email**: <EMAIL> (PGP key available)
- **GitHub**: Private security advisory (preferred)
- **Bug Bounty**: Coordinated disclosure program

### What to Include

- **Description**: Clear explanation of the vulnerability
- **Impact**: Potential security implications
- **Reproduction**: Step-by-step reproduction instructions
- **Environment**: Version, configuration, and system details

### Response Timeline

- **Acknowledgment**: Within 24 hours
- **Initial Assessment**: Within 72 hours
- **Status Updates**: Weekly until resolution
- **Fix Deployment**: Based on severity (P0: 24h, P1: 7d, P2: 30d)

## Security Updates

### Update Policy

- **Critical Security Patches**: Immediate release and notification
- **Regular Security Updates**: Monthly security review and updates
- **Dependency Updates**: Automated security dependency updates
- **End-of-Life Policy**: 2 years security support for major versions

### Notification Channels

- **Security Advisories**: GitHub security advisories
- **Release Notes**: Detailed security fix descriptions
- **Community Updates**: Home Assistant community forum posts
- **Direct Notification**: Email alerts for critical vulnerabilities

---

**Last Updated**: 2025-07-11
**Security Contact**: <EMAIL>
**PGP Fingerprint**: [To be added]
