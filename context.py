import logging
from typing import Any, Dict
from homeassistant.core import HomeAssistant
from homeassistant.helpers import device_registry, area_registry

_LOGGER = logging.getLogger(__name__)

def validate_ws_origin(origin: str, allowed_origins: list[str]) -> bool:
    """
    Validate WebSocket origin against allowed list.
    """
    if not origin or not allowed_origins:
        return False
    return origin in allowed_origins

# GOAL:
# Collect all relevant context from the Hass instance.
# entity states, automations, devices, and areas.

def redact(log_dict: Dict[str, Any]) -> Dict[str, Any]:
    """
    Async-safe redaction helper for sensitive attributes.
    Redacts common sensitive fields like license_key, password, token, etc.
    """
    sensitive_keys = {
        'license_key', 'license', 'password', 'token', 'api_key', 'secret',
        'auth', 'credential', 'private'
    }
    
    redacted = {}
    for key, value in log_dict.items():
        key_lower = key.lower()
        if any(sensitive in key_lower for sensitive in sensitive_keys):
            redacted[key] = "[REDACTED]"
        elif isinstance(value, dict):
            redacted[key] = redact(value)
        elif isinstance(value, list):
            redacted[key] = [redact(item) if isinstance(item, dict) else item for item in value]
        else:
            redacted[key] = value
    
    return redacted

def redact_headers(headers: dict) -> dict:
    """
    Redact sensitive headers (e.g., license_key) before logging.
    """
    return {k: ("[REDACTED]" if "license" in k.lower() else v) for k, v in headers.items()}

async def collect_scenes(hass: HomeAssistant) -> list:
    """
    Collect all scene entities from Home Assistant.
    Returns list of scene information with redacted attributes.
    """
    try:
        scenes = hass.states.async_all("scene")
        scene_list = []
        
        for scene in scenes:
            scene_info = {
                "entity_id": scene.entity_id,
                "name": scene.attributes.get("friendly_name", scene.entity_id),
                "state": scene.state,
                "attributes": redact(dict(scene.attributes))
            }
            scene_list.append(scene_info)
        
        _LOGGER.debug("Collected %d scenes", len(scene_list))
        return scene_list
        
    except Exception as e:
        _LOGGER.error("Failed to collect scenes: %s", str(e))
        raise

async def collect_zones(hass: HomeAssistant) -> list:
    """
    Collect all zone entities from Home Assistant.
    Returns list of zone information with redacted attributes.
    """
    try:
        zones = hass.states.async_all("zone")
        zone_list = []
        
        for zone in zones:
            zone_info = {
                "entity_id": zone.entity_id,
                "name": zone.attributes.get("friendly_name", zone.entity_id),
                "state": zone.state,
                "latitude": zone.attributes.get("latitude"),
                "longitude": zone.attributes.get("longitude"),
                "radius": zone.attributes.get("radius"),
                "attributes": redact(dict(zone.attributes))
            }
            zone_list.append(zone_info)
        
        _LOGGER.debug("Collected %d zones", len(zone_list))
        return zone_list
        
    except Exception as e:
        _LOGGER.error("Failed to collect zones: %s", str(e))
        raise

async def collect_context(hass: HomeAssistant) -> dict:
    """
    Collect all relevant context from the Hass instance.
    Ensures all WS queries are properly awaited.
    Updated schema includes scenes and zones.
    """
    try:
        # Fetch all entities, their states, and attributes with redaction
        entity_states = []
        for e in hass.states.async_all():
            entity_info = {
                "entity_id": e.entity_id,
                "state": e.state,
                "attributes": redact(dict(e.attributes))
            }
            entity_states.append(entity_info)

        # Get all automation entities via Hass's native API
        automation_states = hass.states.async_all("automation")
        automations = [auto.entity_id for auto in automation_states]

        # Device and area registries - ensure proper awaiting
        dev_registry = device_registry.async_get(hass)
        devices = list(dev_registry.devices.values())

        area_reg = area_registry.async_get(hass)
        areas = list(area_reg.areas.values())

        # Collect scenes and zones
        scenes = await collect_scenes(hass)
        zones = await collect_zones(hass)

        # Format collected data with updated schema
        context_data = {
            "entities": entity_states,
            "automations": automations,
            "devices": [d.name for d in devices if d.name],
            "areas": [a.name for a in areas if a.name],
            "scenes": scenes,
            "zones": zones
        }

        # Log redacted version for debugging
        _LOGGER.debug("Collected context: %s", redact(context_data))
        
        return context_data

    except Exception as e:
        _LOGGER.error("Failed to collect context: %s", str(e))
        raise
